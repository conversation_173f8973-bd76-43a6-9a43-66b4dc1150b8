"""
测试Ella基于坐标的修复方案
验证改进后的输入和发送功能
"""
import sys
import os

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

from pages.apps.ella.dialogue_page import EllaDialoguePage
from core.logger import log
import time


def test_ella_coordinate_fix():
    """测试Ella基于坐标的修复方案"""
    log.info("🧪 开始测试Ella基于坐标的修复方案")
    
    # 初始化页面对象
    ella_page = EllaDialoguePage()
    
    try:
        # 1. 启动应用
        log.info("1️⃣ 启动Ella应用")
        if not ella_page.start_app_with_activity():
            log.error("❌ Ella应用启动失败")
            return False
        
        log.info("✅ Ella应用启动成功")
        
        # 2. 等待页面加载
        log.info("2️⃣ 等待页面加载")
        if not ella_page.wait_for_page_load(timeout=15):
            log.warning("⚠️ 页面加载超时，但继续测试")
        else:
            log.info("✅ 页面加载成功")
        
        # 3. 获取页面截图
        log.info("3️⃣ 获取当前页面截图")
        screenshot_path = ella_page.screenshot("ella_coordinate_test_before.png")
        log.info(f"📸 截图保存: {screenshot_path}")
        
        # 4. 测试改进后的输入框检测
        log.info("4️⃣ 测试改进后的输入框检测")
        input_ready = ella_page.ensure_input_box_ready()
        
        if input_ready:
            log.info("✅ 输入框检测成功")
            
            # 5. 测试基于坐标的文本输入
            log.info("5️⃣ 测试基于坐标的文本输入")
            test_commands = [
                "你好",
                "测试输入",
                "open bluetooth",
                "what time is it"
            ]
            
            for i, command in enumerate(test_commands):
                log.info(f"测试命令 {i+1}: {command}")
                
                # 输入命令
                if ella_page.input_text_command(command):
                    log.info(f"✅ 命令输入成功: {command}")
                    
                    # 等待一下
                    time.sleep(1)
                    
                    # 测试发送功能
                    if ella_page.send_command():
                        log.info(f"✅ 命令发送成功: {command}")
                        
                        # 等待响应
                        time.sleep(3)
                        
                        # 获取响应后截图
                        screenshot_path = ella_page.screenshot(f"ella_response_{i+1}.png")
                        log.info(f"📸 响应截图: {screenshot_path}")
                        
                    else:
                        log.error(f"❌ 命令发送失败: {command}")
                else:
                    log.error(f"❌ 命令输入失败: {command}")
                
                # 命令间隔
                time.sleep(2)
            
            # 6. 测试完整的命令执行流程
            log.info("6️⃣ 测试完整的命令执行流程")
            final_command = "测试完整流程"
            
            if ella_page.execute_text_command(final_command):
                log.info(f"✅ 完整流程测试成功: {final_command}")
                
                # 等待响应
                time.sleep(3)
                
                # 获取最终截图
                final_screenshot = ella_page.screenshot("ella_final_test.png")
                log.info(f"📸 最终截图: {final_screenshot}")
                
            else:
                log.error(f"❌ 完整流程测试失败: {final_command}")
            
        else:
            log.error("❌ 输入框检测失败")
            
            # 获取失败时的截图
            screenshot_path = ella_page.screenshot("ella_coordinate_test_failed.png")
            log.info(f"📸 失败截图: {screenshot_path}")
        
        # 7. 输出测试总结
        log.info("7️⃣ 输出测试总结")
        _output_test_summary(ella_page, input_ready)
        
        return input_ready
        
    except Exception as e:
        log.error(f"测试过程中发生异常: {e}")
        return False
    
    finally:
        # 清理
        try:
            ella_page.stop_app()
            log.info("✅ 应用已停止")
        except:
            log.warning("应用停止失败")


def _output_test_summary(ella_page, input_ready):
    """输出测试总结"""
    try:
        log.info("📋 测试总结:")
        
        # 基本信息
        current_app = ella_page.driver.app_current()
        screen_size = ella_page.driver.window_size()
        
        log.info(f"当前应用: {current_app.get('package', 'Unknown')}")
        log.info(f"屏幕尺寸: {screen_size}")
        
        # 测试结果
        if input_ready:
            log.info("✅ 输入框检测: 成功")
            log.info("✅ 基于坐标的交互方案: 可用")
            log.info("✅ 修复方案: 有效")
        else:
            log.info("❌ 输入框检测: 失败")
            log.info("❌ 基于坐标的交互方案: 不可用")
            log.info("❌ 修复方案: 无效")
        
        # 建议
        log.info("💡 建议:")
        if input_ready:
            log.info("  - 可以使用基于坐标的交互方案进行Ella测试")
            log.info("  - 建议在实际测试中验证响应内容")
            log.info("  - 可以考虑添加更多的错误处理机制")
        else:
            log.info("  - 需要进一步调试Ella应用的交互问题")
            log.info("  - 可能需要检查应用权限或版本兼容性")
            log.info("  - 建议手动操作应用确认功能正常")
        
    except Exception as e:
        log.error(f"输出测试总结失败: {e}")


def test_specific_coordinate_interaction():
    """测试特定坐标交互"""
    log.info("🎯 测试特定坐标交互")
    
    ella_page = EllaDialoguePage()
    
    try:
        # 启动应用
        if not ella_page.start_app_with_activity():
            return False
        
        time.sleep(3)
        
        # 获取屏幕尺寸
        screen_width, screen_height = ella_page.driver.window_size()
        log.info(f"屏幕尺寸: {screen_width} x {screen_height}")
        
        # 测试发现的坐标
        test_coordinates = [
            (540, 2160),  # 发现的可交互坐标
            (screen_width // 2, int(screen_height * 0.9)),  # 底部中央
            (screen_width // 2, int(screen_height * 0.85)), # 底部偏上
            (int(screen_width * 0.8), int(screen_height * 0.9)), # 底部右侧
        ]
        
        for i, (x, y) in enumerate(test_coordinates):
            log.info(f"测试坐标 {i+1}: ({x}, {y})")
            
            try:
                # 点击坐标
                ella_page.driver.click(x, y)
                time.sleep(1)
                
                # 尝试输入
                ella_page.driver.send_keys(f"test{i+1}")
                time.sleep(1)
                
                # 清除
                ella_page.driver.press("ctrl+a")
                ella_page.driver.press("del")
                
                log.info(f"✅ 坐标 ({x}, {y}) 交互成功")
                
            except Exception as e:
                log.warning(f"坐标 ({x}, {y}) 交互失败: {e}")
        
        return True
        
    except Exception as e:
        log.error(f"特定坐标交互测试失败: {e}")
        return False
    
    finally:
        try:
            ella_page.stop_app()
        except:
            pass


def main():
    """主函数"""
    log.info("🚀 开始Ella基于坐标的修复方案测试")
    
    # 测试1: 完整功能测试
    success1 = test_ella_coordinate_fix()
    
    # 测试2: 特定坐标交互测试
    success2 = test_specific_coordinate_interaction()
    
    # 总结
    if success1 and success2:
        log.info("🎉 Ella基于坐标的修复方案测试全部通过")
        return 0
    elif success1 or success2:
        log.info("⚠️ Ella基于坐标的修复方案部分测试通过")
        return 0
    else:
        log.error("💥 Ella基于坐标的修复方案测试失败")
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
