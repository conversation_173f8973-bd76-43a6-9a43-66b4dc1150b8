"""
测试错误处理修复
验证改进后的错误处理和重试机制
"""
import sys
import os

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

from pages.apps.ella.dialogue_page import EllaDialoguePage
from core.logger import log
import time


def test_error_handling_fix():
    """测试错误处理修复"""
    log.info("🧪 开始测试错误处理修复")
    
    # 初始化页面对象
    ella_page = EllaDialoguePage()
    
    try:
        # 1. 测试应用启动
        log.info("1️⃣ 测试应用启动")
        if not ella_page.start_app_with_activity():
            log.error("❌ Ella应用启动失败")
            return False
        
        log.info("✅ Ella应用启动成功")
        
        # 2. 测试页面加载（这里应该不再出现Invalid version错误）
        log.info("2️⃣ 测试页面加载（错误处理改进）")
        page_loaded = ella_page.wait_for_page_load(timeout=20)
        
        if page_loaded:
            log.info("✅ 页面加载成功，没有出现Invalid version错误")
        else:
            log.warning("⚠️ 页面加载失败，但这可能是正常的")
        
        # 3. 测试元素检测的错误处理
        log.info("3️⃣ 测试元素检测的错误处理")
        test_element_error_handling(ella_page)
        
        # 4. 测试输入框检测
        log.info("4️⃣ 测试输入框检测")
        input_ready = ella_page.ensure_input_box_ready()
        
        if input_ready:
            log.info("✅ 输入框检测成功")
            
            # 5. 测试简单的文本输入
            log.info("5️⃣ 测试简单的文本输入")
            if ella_page.input_text_command("测试错误处理"):
                log.info("✅ 文本输入成功")
                
                # 6. 测试发送功能
                if ella_page.send_command():
                    log.info("✅ 发送功能正常")
                else:
                    log.warning("⚠️ 发送功能异常")
            else:
                log.warning("⚠️ 文本输入失败")
        else:
            log.warning("⚠️ 输入框检测失败")
        
        # 7. 获取测试截图
        screenshot_path = ella_page.screenshot("error_handling_test.png")
        log.info(f"📸 测试截图: {screenshot_path}")
        
        return True
        
    except Exception as e:
        log.error(f"测试过程中发生异常: {e}")
        return False
    
    finally:
        # 清理
        try:
            ella_page.stop_app()
            log.info("✅ 应用已停止")
        except:
            log.warning("应用停止失败")


def test_element_error_handling(ella_page):
    """测试元素检测的错误处理"""
    try:
        log.info("测试元素检测的错误处理...")
        
        # 测试各种元素的is_exists方法
        elements_to_test = [
            ("输入框", ella_page.input_box),
            ("文本输入框", ella_page.text_input_box),
            ("语音按钮", ella_page.voice_input_button),
            ("发送按钮", ella_page.send_button),
            ("欢迎消息", ella_page.ella_greeting)
        ]
        
        success_count = 0
        error_count = 0
        
        for name, element in elements_to_test:
            try:
                exists = element.is_exists()
                log.info(f"  {name}: {'存在' if exists else '不存在'}")
                success_count += 1
            except Exception as e:
                log.warning(f"  {name}: 检测失败 - {e}")
                error_count += 1
        
        log.info(f"元素检测结果: 成功 {success_count}, 错误 {error_count}")
        
        if error_count == 0:
            log.info("✅ 所有元素检测都没有出现错误")
        elif error_count < len(elements_to_test) / 2:
            log.info("✅ 大部分元素检测成功，错误处理有效")
        else:
            log.warning("⚠️ 多数元素检测失败，可能需要进一步优化")
        
    except Exception as e:
        log.error(f"元素错误处理测试失败: {e}")


def test_service_health_check():
    """测试服务健康检查"""
    log.info("🔍 测试服务健康检查")
    
    ella_page = EllaDialoguePage()
    
    try:
        # 启动应用
        if not ella_page.start_app_with_activity():
            return False
        
        # 测试服务健康检查
        log.info("测试UIAutomator2服务健康检查...")
        
        health_check_results = []
        
        for i in range(5):
            try:
                is_healthy = ella_page._ensure_service_health()
                health_check_results.append(is_healthy)
                log.info(f"健康检查 {i+1}: {'通过' if is_healthy else '失败'}")
                time.sleep(1)
            except Exception as e:
                log.warning(f"健康检查 {i+1} 异常: {e}")
                health_check_results.append(False)
        
        success_rate = sum(health_check_results) / len(health_check_results) * 100
        log.info(f"服务健康检查成功率: {success_rate:.1f}%")
        
        if success_rate >= 80:
            log.info("✅ 服务健康检查表现良好")
        elif success_rate >= 50:
            log.info("⚠️ 服务健康检查表现一般")
        else:
            log.warning("❌ 服务健康检查表现较差")
        
        return success_rate >= 50
        
    except Exception as e:
        log.error(f"服务健康检查测试失败: {e}")
        return False
    
    finally:
        try:
            ella_page.stop_app()
        except:
            pass


def test_multiple_runs():
    """测试多次运行的稳定性"""
    log.info("🔄 测试多次运行的稳定性")
    
    success_count = 0
    total_runs = 3
    
    for run in range(total_runs):
        log.info(f"开始第 {run + 1} 次运行...")
        
        try:
            if test_error_handling_fix():
                success_count += 1
                log.info(f"✅ 第 {run + 1} 次运行成功")
            else:
                log.warning(f"⚠️ 第 {run + 1} 次运行失败")
        except Exception as e:
            log.error(f"❌ 第 {run + 1} 次运行异常: {e}")
        
        # 运行间隔
        if run < total_runs - 1:
            time.sleep(3)
    
    success_rate = success_count / total_runs * 100
    log.info(f"多次运行成功率: {success_rate:.1f}% ({success_count}/{total_runs})")
    
    return success_rate >= 66  # 至少2/3成功


def main():
    """主函数"""
    log.info("🚀 开始错误处理修复测试")
    
    # 测试1: 基本错误处理
    test1_success = test_error_handling_fix()
    
    # 测试2: 服务健康检查
    test2_success = test_service_health_check()
    
    # 测试3: 多次运行稳定性
    test3_success = test_multiple_runs()
    
    # 总结
    tests_passed = sum([test1_success, test2_success, test3_success])
    
    log.info("📊 测试结果总结:")
    log.info(f"  基本错误处理: {'✅ 通过' if test1_success else '❌ 失败'}")
    log.info(f"  服务健康检查: {'✅ 通过' if test2_success else '❌ 失败'}")
    log.info(f"  多次运行稳定性: {'✅ 通过' if test3_success else '❌ 失败'}")
    log.info(f"  总体通过率: {tests_passed}/3")
    
    if tests_passed >= 2:
        log.info("🎉 错误处理修复测试总体通过")
        return 0
    else:
        log.error("💥 错误处理修复测试失败")
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
