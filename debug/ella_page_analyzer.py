"""
Ella页面深度分析工具
分析Ella应用的页面结构和内容
"""
import sys
import os

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

from pages.apps.ella.dialogue_page import EllaDialoguePage
from core.logger import log
import time
import json


def analyze_ella_page():
    """深度分析Ella页面"""
    log.info("🔍 开始深度分析Ella页面")
    
    ella_page = EllaDialoguePage()
    
    try:
        # 1. 启动应用
        log.info("1️⃣ 启动Ella应用")
        if not ella_page.start_app_with_activity():
            log.error("❌ Ella应用启动失败")
            return
        
        # 2. 等待页面稳定
        log.info("2️⃣ 等待页面稳定")
        time.sleep(5)
        
        # 3. 获取页面源码
        log.info("3️⃣ 获取页面源码")
        page_source = get_page_source(ella_page)
        
        # 4. 分析页面结构
        log.info("4️⃣ 分析页面结构")
        analyze_page_structure(ella_page)
        
        # 5. 尝试不同的交互方式
        log.info("5️⃣ 尝试不同的交互方式")
        try_different_interactions(ella_page)
        
        # 6. 检查应用状态
        log.info("6️⃣ 检查应用状态")
        check_app_state(ella_page)
        
        # 7. 保存分析结果
        log.info("7️⃣ 保存分析结果")
        save_analysis_results(ella_page, page_source)
        
    except Exception as e:
        log.error(f"分析过程中发生异常: {e}")
    
    finally:
        try:
            ella_page.stop_app()
        except:
            pass


def get_page_source(ella_page):
    """获取页面源码"""
    try:
        log.info("获取页面源码...")
        
        # 方法1: 使用dump_hierarchy
        try:
            page_source = ella_page.driver.dump_hierarchy()
            if page_source:
                log.info(f"✅ 获取页面源码成功，长度: {len(page_source)}")
                
                # 保存到文件
                with open("debug/ella_page_source.xml", "w", encoding="utf-8") as f:
                    f.write(page_source)
                log.info("页面源码已保存到: debug/ella_page_source.xml")
                
                return page_source
            else:
                log.warning("页面源码为空")
        except Exception as e:
            log.error(f"获取页面源码失败: {e}")
        
        return None
        
    except Exception as e:
        log.error(f"获取页面源码异常: {e}")
        return None


def analyze_page_structure(ella_page):
    """分析页面结构"""
    try:
        log.info("分析页面结构...")
        
        # 获取所有可能的元素类型
        element_types = [
            "android.widget.TextView",
            "android.widget.EditText", 
            "android.widget.Button",
            "android.widget.ImageButton",
            "android.widget.ImageView",
            "android.widget.LinearLayout",
            "android.widget.RelativeLayout",
            "android.widget.FrameLayout",
            "android.widget.ScrollView",
            "androidx.recyclerview.widget.RecyclerView",
            "android.view.View",
            "android.view.ViewGroup"
        ]
        
        total_elements = 0
        
        for element_type in element_types:
            try:
                elements = ella_page.driver(className=element_type)
                count = elements.count if elements.exists() else 0
                total_elements += count
                
                if count > 0:
                    log.info(f"  ✅ {element_type}: {count} 个")
                    
                    # 获取前几个元素的详细信息
                    if count > 0:
                        try:
                            for i in range(min(3, count)):
                                element = elements[i] if count > 1 else elements
                                info = element.info
                                text = info.get('text', '')
                                resource_id = info.get('resourceId', '')
                                bounds = info.get('bounds', {})
                                
                                log.info(f"    元素{i+1}: text='{text}', resourceId='{resource_id}', bounds={bounds}")
                        except Exception as e:
                            log.debug(f"获取元素详细信息失败: {e}")
                else:
                    log.debug(f"  ❌ {element_type}: 0 个")
                    
            except Exception as e:
                log.debug(f"检查元素类型 {element_type} 失败: {e}")
        
        log.info(f"页面总元素数量: {total_elements}")
        
        if total_elements == 0:
            log.warning("⚠️ 页面没有检测到任何UI元素，可能是:")
            log.warning("  1. 页面还在加载中")
            log.warning("  2. 应用启动到了错误的页面")
            log.warning("  3. 应用需要特殊权限或设置")
            log.warning("  4. UIAutomator2无法访问该页面")
        
    except Exception as e:
        log.error(f"分析页面结构失败: {e}")


def try_different_interactions(ella_page):
    """尝试不同的交互方式"""
    try:
        log.info("尝试不同的交互方式...")
        
        # 1. 尝试点击屏幕中央
        log.info("1. 尝试点击屏幕中央")
        screen_width, screen_height = ella_page.driver.window_size()
        center_x, center_y = screen_width // 2, screen_height // 2
        
        ella_page.driver.click(center_x, center_y)
        time.sleep(2)
        
        # 检查是否有变化
        elements_after_click = ella_page.driver(className="android.widget.TextView")
        if elements_after_click.exists():
            log.info(f"✅ 点击后检测到 {elements_after_click.count} 个TextView元素")
        
        # 2. 尝试按返回键
        log.info("2. 尝试按返回键")
        ella_page.driver.press("back")
        time.sleep(2)
        
        # 3. 尝试按Home键再重新进入
        log.info("3. 尝试按Home键再重新进入")
        ella_page.driver.press("home")
        time.sleep(1)
        
        # 重新启动应用
        ella_page.driver.app_start("com.transsion.aivoiceassistant")
        time.sleep(3)
        
        # 4. 尝试滑动操作
        log.info("4. 尝试滑动操作")
        # 向上滑动
        ella_page.driver.swipe(center_x, center_y + 200, center_x, center_y - 200, duration=0.5)
        time.sleep(1)
        
        # 向下滑动
        ella_page.driver.swipe(center_x, center_y - 200, center_x, center_y + 200, duration=0.5)
        time.sleep(1)
        
        # 5. 尝试长按
        log.info("5. 尝试长按屏幕")
        ella_page.driver.long_click(center_x, center_y)
        time.sleep(2)
        
        # 6. 检查最终状态
        final_elements = ella_page.driver(className="android.widget.TextView")
        if final_elements.exists():
            log.info(f"✅ 交互后检测到 {final_elements.count} 个TextView元素")
        else:
            log.warning("⚠️ 所有交互尝试后仍未检测到UI元素")
        
    except Exception as e:
        log.error(f"尝试交互失败: {e}")


def check_app_state(ella_page):
    """检查应用状态"""
    try:
        log.info("检查应用状态...")
        
        # 1. 检查当前应用
        current_app = ella_page.driver.app_current()
        log.info(f"当前应用: {current_app}")
        
        # 2. 检查应用是否在运行
        running_apps = ella_page.driver.app_list_running()
        if "com.transsion.aivoiceassistant" in running_apps:
            log.info("✅ Ella应用正在运行")
        else:
            log.warning("⚠️ Ella应用未在运行列表中")
        
        # 3. 检查应用信息
        try:
            app_info = ella_page.driver.app_info("com.transsion.aivoiceassistant")
            log.info(f"应用信息: {app_info}")
        except Exception as e:
            log.warning(f"获取应用信息失败: {e}")
        
        # 4. 检查设备状态
        device_info = ella_page.driver.device_info
        log.info(f"设备信息: {device_info}")
        
        # 5. 检查屏幕状态
        screen_on = ella_page.driver.info.get('screenOn', False)
        log.info(f"屏幕状态: {'开启' if screen_on else '关闭'}")
        
    except Exception as e:
        log.error(f"检查应用状态失败: {e}")


def save_analysis_results(ella_page, page_source):
    """保存分析结果"""
    try:
        log.info("保存分析结果...")
        
        # 1. 保存截图
        screenshot_path = ella_page.screenshot("ella_analysis_result.png")
        log.info(f"分析截图: {screenshot_path}")
        
        # 2. 保存设备信息
        device_info = {
            "current_app": ella_page.driver.app_current(),
            "device_info": ella_page.driver.device_info,
            "screen_size": ella_page.driver.window_size(),
            "running_apps": ella_page.driver.app_list_running()[:10]  # 只保存前10个
        }
        
        with open("debug/ella_device_info.json", "w", encoding="utf-8") as f:
            json.dump(device_info, f, indent=2, ensure_ascii=False)
        log.info("设备信息已保存到: debug/ella_device_info.json")
        
        # 3. 生成分析报告
        report = generate_analysis_report(page_source, device_info)
        with open("debug/ella_analysis_report.txt", "w", encoding="utf-8") as f:
            f.write(report)
        log.info("分析报告已保存到: debug/ella_analysis_report.txt")
        
    except Exception as e:
        log.error(f"保存分析结果失败: {e}")


def generate_analysis_report(page_source, device_info):
    """生成分析报告"""
    report = []
    report.append("=" * 50)
    report.append("Ella应用页面分析报告")
    report.append("=" * 50)
    report.append("")
    
    # 设备信息
    report.append("📱 设备信息:")
    report.append(f"  当前应用: {device_info.get('current_app', {}).get('package', 'Unknown')}")
    report.append(f"  屏幕尺寸: {device_info.get('screen_size', 'Unknown')}")
    report.append(f"  设备型号: {device_info.get('device_info', {}).get('model', 'Unknown')}")
    report.append("")
    
    # 页面源码分析
    report.append("📄 页面源码分析:")
    if page_source:
        report.append(f"  源码长度: {len(page_source)} 字符")
        if "com.transsion.aivoiceassistant" in page_source:
            report.append("  ✅ 包含Ella应用包名")
        else:
            report.append("  ❌ 不包含Ella应用包名")
        
        if "EditText" in page_source:
            report.append("  ✅ 包含输入框元素")
        else:
            report.append("  ❌ 不包含输入框元素")
    else:
        report.append("  ❌ 无法获取页面源码")
    report.append("")
    
    # 问题诊断
    report.append("🔍 问题诊断:")
    if not page_source or len(page_source) < 100:
        report.append("  ⚠️ 页面源码过短或为空，可能原因:")
        report.append("    - 应用启动到了加载页面")
        report.append("    - 应用需要用户交互才能进入主界面")
        report.append("    - 应用权限不足")
        report.append("    - UIAutomator2无法访问该应用")
    
    # 建议解决方案
    report.append("")
    report.append("💡 建议解决方案:")
    report.append("  1. 检查应用是否需要首次设置或登录")
    report.append("  2. 尝试手动操作应用到主界面")
    report.append("  3. 检查应用权限设置")
    report.append("  4. 尝试不同的启动Activity")
    report.append("  5. 检查应用版本兼容性")
    
    return "\n".join(report)


if __name__ == "__main__":
    analyze_ella_page()
