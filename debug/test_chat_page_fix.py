"""
测试对话页面检测修复
验证改进后的ensure_on_chat_page方法
"""
import sys
import os

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

from pages.apps.ella.dialogue_page import EllaDialoguePage
from core.logger import log
import time


def test_chat_page_detection():
    """测试对话页面检测"""
    log.info("🧪 开始测试对话页面检测修复")
    
    # 初始化页面对象
    ella_page = EllaDialoguePage()
    
    try:
        # 1. 启动应用
        log.info("1️⃣ 启动Ella应用")
        if not ella_page.start_app_with_activity():
            log.error("❌ Ella应用启动失败")
            return False
        
        log.info("✅ Ella应用启动成功")
        
        # 2. 等待页面加载
        log.info("2️⃣ 等待页面加载")
        if not ella_page.wait_for_page_load(timeout=15):
            log.warning("⚠️ 页面加载超时，但继续测试")
        else:
            log.info("✅ 页面加载成功")
        
        # 3. 测试对话页面指示器检测
        log.info("3️⃣ 测试对话页面指示器检测")
        indicators_detected = ella_page._check_chat_page_indicators()
        log.info(f"对话页面指示器检测结果: {indicators_detected}")
        
        # 4. 测试ensure_on_chat_page方法
        log.info("4️⃣ 测试ensure_on_chat_page方法")
        chat_page_result = ella_page.ensure_on_chat_page()
        
        if chat_page_result:
            log.info("✅ ensure_on_chat_page 返回 True")
        else:
            log.error("❌ ensure_on_chat_page 返回 False")
        
        # 5. 测试多次调用的稳定性
        log.info("5️⃣ 测试多次调用的稳定性")
        success_count = 0
        total_attempts = 5
        
        for i in range(total_attempts):
            try:
                result = ella_page.ensure_on_chat_page()
                if result:
                    success_count += 1
                log.info(f"第 {i+1} 次调用: {'成功' if result else '失败'}")
                time.sleep(1)
            except Exception as e:
                log.warning(f"第 {i+1} 次调用异常: {e}")
        
        success_rate = success_count / total_attempts * 100
        log.info(f"多次调用成功率: {success_rate:.1f}% ({success_count}/{total_attempts})")
        
        # 6. 测试在不同状态下的检测
        log.info("6️⃣ 测试在不同状态下的检测")
        test_different_states(ella_page)
        
        # 7. 获取测试截图
        screenshot_path = ella_page.screenshot("chat_page_detection_test.png")
        log.info(f"📸 测试截图: {screenshot_path}")
        
        return chat_page_result and success_rate >= 80
        
    except Exception as e:
        log.error(f"测试过程中发生异常: {e}")
        return False
    
    finally:
        # 清理
        try:
            ella_page.stop_app()
            log.info("✅ 应用已停止")
        except:
            log.warning("应用停止失败")


def test_different_states(ella_page):
    """测试在不同状态下的检测"""
    try:
        log.info("测试在不同状态下的对话页面检测...")
        
        # 状态1: 当前状态
        log.info("状态1: 当前状态")
        result1 = ella_page.ensure_on_chat_page()
        log.info(f"  当前状态检测: {'成功' if result1 else '失败'}")
        
        # 状态2: 按Home键后
        log.info("状态2: 按Home键后")
        ella_page.driver.press("home")
        time.sleep(2)
        
        result2 = ella_page.ensure_on_chat_page()
        log.info(f"  Home键后检测: {'成功' if result2 else '失败'}")
        
        # 状态3: 重新进入应用后
        log.info("状态3: 重新进入应用后")
        ella_page.driver.app_start("com.transsion.aivoiceassistant")
        time.sleep(3)
        
        result3 = ella_page.ensure_on_chat_page()
        log.info(f"  重新进入后检测: {'成功' if result3 else '失败'}")
        
        # 状态4: 点击屏幕后
        log.info("状态4: 点击屏幕后")
        screen_width, screen_height = ella_page.driver.window_size()
        ella_page.driver.click(screen_width // 2, screen_height // 2)
        time.sleep(1)
        
        result4 = ella_page.ensure_on_chat_page()
        log.info(f"  点击屏幕后检测: {'成功' if result4 else '失败'}")
        
        # 总结不同状态的检测结果
        results = [result1, result2, result3, result4]
        success_count = sum(results)
        log.info(f"不同状态检测总结: {success_count}/4 成功")
        
        return success_count >= 3  # 至少3/4成功
        
    except Exception as e:
        log.error(f"不同状态测试失败: {e}")
        return False


def test_chat_page_indicators():
    """专门测试对话页面指示器"""
    log.info("🎯 专门测试对话页面指示器")
    
    ella_page = EllaDialoguePage()
    
    try:
        # 启动应用
        if not ella_page.start_app_with_activity():
            return False
        
        time.sleep(3)
        
        # 测试各种指示器
        log.info("测试各种对话页面指示器...")
        
        indicators = {
            "输入框检测": False,
            "发送按钮检测": False,
            "语音按钮检测": False,
            "通用UI元素检测": False,
            "页面文本检测": False
        }
        
        # 输入框检测
        try:
            if ella_page.input_box.is_exists() or ella_page.text_input_box.is_exists():
                indicators["输入框检测"] = True
        except Exception as e:
            log.debug(f"输入框检测异常: {e}")
        
        # 发送按钮检测
        try:
            if ella_page.send_button.is_exists():
                indicators["发送按钮检测"] = True
        except Exception as e:
            log.debug(f"发送按钮检测异常: {e}")
        
        # 语音按钮检测
        try:
            if ella_page.voice_input_button.is_exists() or ella_page.voice_button_alt.is_exists():
                indicators["语音按钮检测"] = True
        except Exception as e:
            log.debug(f"语音按钮检测异常: {e}")
        
        # 通用UI元素检测
        try:
            edit_texts = ella_page.driver(className="android.widget.EditText")
            if edit_texts.exists():
                indicators["通用UI元素检测"] = True
        except Exception as e:
            log.debug(f"通用UI元素检测异常: {e}")
        
        # 页面文本检测
        try:
            chat_indicators = ["Hi，我是Ella", "Ella", "输入", "发送", "语音"]
            for indicator in chat_indicators:
                elements = ella_page.driver(textContains=indicator)
                if elements.exists():
                    indicators["页面文本检测"] = True
                    break
        except Exception as e:
            log.debug(f"页面文本检测异常: {e}")
        
        # 输出结果
        log.info("对话页面指示器检测结果:")
        for name, result in indicators.items():
            log.info(f"  {name}: {'✅ 成功' if result else '❌ 失败'}")
        
        success_count = sum(indicators.values())
        total_count = len(indicators)
        success_rate = success_count / total_count * 100
        
        log.info(f"指示器检测成功率: {success_rate:.1f}% ({success_count}/{total_count})")
        
        return success_rate >= 20  # 至少有一个指示器成功
        
    except Exception as e:
        log.error(f"指示器测试失败: {e}")
        return False
    
    finally:
        try:
            ella_page.stop_app()
        except:
            pass


def main():
    """主函数"""
    log.info("🚀 开始对话页面检测修复测试")
    
    # 测试1: 基本对话页面检测
    test1_success = test_chat_page_detection()
    
    # 测试2: 对话页面指示器
    test2_success = test_chat_page_indicators()
    
    # 总结
    tests_passed = sum([test1_success, test2_success])
    
    log.info("📊 测试结果总结:")
    log.info(f"  基本对话页面检测: {'✅ 通过' if test1_success else '❌ 失败'}")
    log.info(f"  对话页面指示器: {'✅ 通过' if test2_success else '❌ 失败'}")
    log.info(f"  总体通过率: {tests_passed}/2")
    
    if tests_passed >= 1:
        log.info("🎉 对话页面检测修复测试总体通过")
        return 0
    else:
        log.error("💥 对话页面检测修复测试失败")
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
