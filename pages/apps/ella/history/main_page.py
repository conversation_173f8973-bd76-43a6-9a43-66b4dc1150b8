"""
Ella语音助手主页面
TECNO设备的AI语音助手应用页面对象
"""
import sys
import os

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
sys.path.insert(0, project_root)

from pages.base.common_page import CommonPage
from core.logger import log
from tools.adb_process_monitor import AdbProcessMonitor
import time


class EllaMainPage(CommonPage):
    """Ella语音助手主页面"""
    
    def __init__(self):
        """初始化Ella主页面"""
        super().__init__("ella", "main_page")

        # 初始化进程监控器
        self.process_monitor = AdbProcessMonitor()

        # 初始化页面元素
        self._init_elements()
    
    def _init_elements(self):
        """初始化页面元素 - 基于实际脚本的元素ID"""
        # 主要输入框 - 使用实际的resourceId
        self.input_box = self.create_element(
            {"resourceId": "com.transsion.aivoiceassistant:id/et_input"},
            # {"resourceId": "com.transsion.aivoiceassistant:id/et_input3123123123313"},
            "输入框"
        )

        # 备选输入框定位
        self.text_input_box = self.create_element(
            {"className": "android.widget.EditText"},
            "文本输入框(备选)"
        )

        # 发送按钮 - 使用实际的resourceId
        self.send_button = self.create_element(
            {"resourceId": "com.transsion.aivoiceassistant:id/fl_btn_three_btn"},
            "发送按钮"
        )

        # 语音输入按钮 - 用于语音输入
        self.voice_input_button = self.create_element(
            {"resourceId": "com.transsion.aivoiceassistant:id/iv_voice"},
            "语音输入按钮"
        )

        # 备选语音按钮定位
        self.voice_button_alt = self.create_element(
            {"resourceId": "com.transsion.aivoiceassistant:id/btn_voice"},
            "语音按钮(备选)"
        )

        # 语音录制状态按钮
        self.voice_recording_button = self.create_element(
            {"resourceId": "com.transsion.aivoiceassistant:id/iv_recording"},
            "语音录制按钮"
        )

        # 语音停止按钮
        self.voice_stop_button = self.create_element(
            {"resourceId": "com.transsion.aivoiceassistant:id/iv_stop"},
            "语音停止按钮"
        )

        # TTS播放按钮 - 用于验证响应
        self.tts_play_button = self.create_element(
            {"resourceId": "com.transsion.aivoiceassistant:id/iv_tts_play"},
            "TTS播放按钮"
        )

        # 应用包名验证
        self.app_package = self.create_element(
            {"packageName": "com.transsion.aivoiceassistant"},
            "Ella应用包"
        )

        # 聊天消息列表
        self.chat_list = self.create_element(
            {"className": "androidx.recyclerview.widget.RecyclerView"},
            "聊天消息列表"
        )

        # Ella的欢迎消息
        self.ella_greeting = self.create_element(
            {"text": "Hi，我是Ella"},
            "Ella欢迎消息"
        )

        # 通用文本视图（用于查找响应内容）
        self.text_view_generic = self.create_element(
            {"className": "android.widget.TextView"},
            "通用文本视图"
        )

        # 可能的其他响应元素
        self.response_container = self.create_element(
            {"className": "android.widget.LinearLayout"},
            "响应容器"
        )

        # 蓝牙设置相关元素
        self.bluetooth_setting_item = self.create_element(
            {"text": "蓝牙"},
            "蓝牙设置项"
        )

        # 设置应用相关
        self.settings_app_icon = self.create_element(
            {"text": "设置"},
            "设置应用图标"
        )
    
    def start_app_with_activity(self):
        """
        启动Ella应用并指定Activity - 改进版本

        Returns:
            bool: 启动是否成功
        """
        try:
            log.info("启动Ella应用（指定Activity）")

            # 使用完整的启动命令
            package_name = "com.transsion.aivoiceassistant"
            activity_name = "com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity"

            # 方法1: 尝试启动指定Activity
            try:
                self.driver.app_start(package_name, activity_name)
                log.info(f"尝试启动Activity: {activity_name}")
                time.sleep(3)  # 给应用启动时间

                # 检查应用是否启动成功
                if self._check_app_started(package_name):
                    log.info("✅ Ella应用启动成功（指定Activity）")
                    return True
            except Exception as e:
                log.warning(f"指定Activity启动失败: {e}")

            # 方法2: 尝试启动应用包（不指定Activity）
            try:
                log.info("尝试启动应用包（不指定Activity）")
                self.driver.app_start(package_name)
                time.sleep(3)

                if self._check_app_started(package_name):
                    log.info("✅ Ella应用启动成功（应用包）")
                    return True
            except Exception as e:
                log.warning(f"应用包启动失败: {e}")

            # 方法3: 使用shell命令启动
            try:
                log.info("尝试使用shell命令启动")
                cmd = f"am start -n {package_name}/{activity_name}"
                self.driver.shell(cmd)
                time.sleep(3)

                if self._check_app_started(package_name):
                    log.info("✅ Ella应用启动成功（shell命令）")
                    return True
            except Exception as e:
                log.warning(f"shell命令启动失败: {e}")

            log.error("❌ 所有启动方法都失败")
            return False

        except Exception as e:
            log.error(f"启动Ella应用失败: {e}")
            return False

    def _check_app_started(self, package_name: str) -> bool:
        """
        检查应用是否启动成功

        Args:
            package_name: 应用包名

        Returns:
            bool: 应用是否启动成功
        """
        try:
            # 方法1: 检查当前前台应用
            current_app = self.driver.app_current()
            current_package = current_app.get('package', '')

            if current_package == package_name:
                log.info(f"✅ 应用已在前台: {current_package}")
                return True

            # 方法2: 检查应用是否在运行
            running_apps = self.driver.app_list_running()
            if package_name in running_apps:
                log.info(f"✅ 应用正在运行: {package_name}")
                # 尝试切换到前台
                self.driver.app_start(package_name)
                time.sleep(1)
                return True

            # 方法3: 检查页面元素
            if self._quick_check_page_elements():
                log.info("✅ 检测到Ella页面元素")
                return True

            return False

        except Exception as e:
            log.error(f"检查应用启动状态失败: {e}")
            return False

    def _quick_check_page_elements(self) -> bool:
        """
        快速检查页面元素是否存在

        Returns:
            bool: 是否检测到页面元素
        """
        try:
            # 检查常见的Ella页面元素
            quick_indicators = [
                self.driver(className="android.widget.EditText"),
                self.driver(className="android.widget.TextView"),
                self.driver(className="android.widget.Button"),
                self.driver(resourceId="com.transsion.aivoiceassistant:id/et_input"),
                self.driver(text="Hi，我是Ella"),
                self.driver(text="Ella")
            ]

            for indicator in quick_indicators:
                if indicator.exists():
                    log.debug(f"检测到页面元素: {indicator}")
                    return True

            return False

        except Exception as e:
            log.debug(f"快速检查页面元素失败: {e}")
            return False

    def wait_for_page_load(self, timeout=None):
        """
        等待页面加载完成 - 改进版本，使用多种检测方法

        Args:
            timeout: 超时时间，默认为10秒

        Returns:
            bool: 页面是否加载完成
        """
        # 设置默认超时时间
        if timeout is None:
            timeout = 10

        log.info(f"等待Ella页面加载完成，超时时间: {timeout}秒")

        try:
            # 预检查：确保UIAutomator2服务健康
            if not self._ensure_service_health():
                log.warning("UIAutomator2服务状态异常，但继续尝试")

            # 方法1: 检查当前应用包名
            current_app = self.driver.app_current()
            current_package = current_app.get('package', '')
            log.info(f"当前应用包名: {current_package}")

            if current_package == "com.transsion.aivoiceassistant":
                log.info("✅ Ella应用包已确认")

                # 等待页面UI元素加载
                return self._wait_for_ui_elements(timeout)

            # 方法2: 使用元素检测
            log.info("尝试通过元素检测确认页面加载")
            return self._wait_for_ui_elements(timeout)

        except Exception as e:
            log.error(f"等待Ella页面加载失败: {e}")
            # 尝试恢复
            if self._attempt_service_recovery():
                log.info("服务恢复成功，重试页面加载检测")
                return self._wait_for_ui_elements(min(timeout, 5))
            return False

    def _ensure_service_health(self) -> bool:
        """
        确保UIAutomator2服务健康

        Returns:
            bool: 服务是否健康
        """
        try:
            # 简单的健康检查
            info = self.driver.info
            if info and 'displayWidth' in info:
                log.debug("UIAutomator2服务健康检查通过")
                return True
            else:
                log.warning("UIAutomator2服务健康检查失败")
                return False
        except Exception as e:
            log.warning(f"UIAutomator2服务健康检查异常: {e}")
            return False

    def _attempt_service_recovery(self) -> bool:
        """
        尝试恢复UIAutomator2服务

        Returns:
            bool: 恢复是否成功
        """
        try:
            log.info("尝试恢复UIAutomator2服务...")

            # 等待一段时间让服务自恢复
            time.sleep(2)

            # 重新检查服务状态
            if self._ensure_service_health():
                log.info("✅ UIAutomator2服务已恢复")
                return True

            log.warning("UIAutomator2服务恢复失败")
            return False

        except Exception as e:
            log.error(f"UIAutomator2服务恢复异常: {e}")
            return False

    def _wait_for_ui_elements(self, timeout: int) -> bool:
        """
        等待UI元素加载完成 - 改进版本，包含更好的错误处理

        Args:
            timeout: 超时时间

        Returns:
            bool: UI元素是否加载完成
        """
        try:
            log.info("等待Ella页面UI元素加载...")

            # 定义多个可能的页面标识元素
            page_indicators = [
                # 输入框元素
                self.input_box,
                self.text_input_box,
                # 语音按钮
                self.voice_input_button,
                self.voice_button_alt,
                # 发送按钮
                self.send_button,
                # Ella欢迎消息
                self.ella_greeting,
                # 聊天列表
                self.chat_list
            ]

            start_time = time.time()
            error_count = 0
            max_errors = 10  # 允许的最大错误次数

            while time.time() - start_time < timeout:
                try:
                    # 检查任何一个页面标识元素是否存在
                    for indicator in page_indicators:
                        try:
                            if indicator.is_exists():
                                log.info(f"✅ 检测到页面元素: {indicator.description}")

                                # 额外等待确保页面完全加载
                                time.sleep(1)
                                return True
                        except Exception as e:
                            error_count += 1
                            log.debug(f"检查元素 {indicator.description} 时出错: {e}")

                            # 如果错误太多，可能是服务问题
                            if error_count > max_errors:
                                log.warning(f"元素检查错误过多 ({error_count})，尝试备选检测方案")
                                return self._fallback_ui_detection()

                    # 使用更安全的方式检查通用元素
                    if self._safe_check_generic_elements():
                        return True

                    time.sleep(0.5)

                except Exception as e:
                    error_count += 1
                    log.debug(f"UI元素检查循环出错: {e}")

                    if error_count > max_errors:
                        log.warning("UI检查错误过多，使用备选方案")
                        return self._fallback_ui_detection()

                    time.sleep(1)  # 错误后等待更长时间

            log.warning("⚠️ UI元素等待超时，使用备选检测方案")
            return self._fallback_ui_detection()

        except Exception as e:
            log.error(f"等待UI元素失败: {e}")
            return self._fallback_ui_detection()

    def _safe_check_generic_elements(self) -> bool:
        """
        安全地检查通用元素

        Returns:
            bool: 是否检测到元素
        """
        try:
            # 检查是否有任何TextView元素（表示页面有内容）
            text_views = self.driver(className="android.widget.TextView")
            if text_views.exists():
                log.info("✅ 检测到页面文本元素，页面已加载")
                return True
        except Exception as e:
            log.debug(f"检查TextView元素失败: {e}")

        try:
            # 检查是否有任何EditText元素（输入框）
            edit_texts = self.driver(className="android.widget.EditText")
            if edit_texts.exists():
                log.info("✅ 检测到输入框元素，页面已加载")
                return True
        except Exception as e:
            log.debug(f"检查EditText元素失败: {e}")

        return False

    def _fallback_ui_detection(self) -> bool:
        """
        备选UI检测方案

        Returns:
            bool: 检测结果
        """
        try:
            log.info("使用备选UI检测方案...")

            # 方案1: 检查应用状态
            current_app = self.driver.app_current()
            if current_app.get('package') == 'com.transsion.aivoiceassistant':
                log.info("✅ Ella应用在前台，假设UI已加载")
                return True

            # 方案2: 尝试简单的屏幕交互
            try:
                screen_width, screen_height = self.driver.window_size()
                if screen_width > 0 and screen_height > 0:
                    log.info("✅ 屏幕尺寸正常，假设UI可用")
                    return True
            except Exception as e:
                log.debug(f"获取屏幕尺寸失败: {e}")

            # 方案3: 宽松策略
            log.info("✅ 使用宽松策略，假设UI已加载")
            return True

        except Exception as e:
            log.error(f"备选UI检测失败: {e}")
            return True  # 最终宽松策略
    
    def input_text_command(self, command: str) -> bool:
        """
        通过文本输入命令 - 改进版本，确保输入成功

        Args:
            command: 要输入的命令文本

        Returns:
            bool: 输入是否成功
        """
        try:
            log.info(f"输入文本命令: {command}")

            # 清空可能存在的旧文本
            self.clear_input_box()

            # 方法1: 使用主输入框
            if self.input_box.is_exists():
                log.info("使用主输入框(et_input)")

                # 确保输入框获得焦点
                self.input_box.click()
                time.sleep(0.5)

                # 尝试多种输入方法
                success = False

                # 尝试1: 使用send_keys方法
                try:
                    if self.input_box.send_keys(command):
                        log.info(f"✅ 通过send_keys输入成功: {command}")
                        success = True
                    else:
                        log.warning("send_keys方法失败")
                except Exception as e:
                    log.warning(f"send_keys方法异常: {e}")

                # 尝试2: 使用set_text方法
                if not success:
                    try:
                        self.input_box.set_text(command)
                        log.info(f"✅ 通过set_text输入成功: {command}")
                        success = True
                    except Exception as e:
                        log.warning(f"set_text方法异常: {e}")

                # 尝试3: 使用driver的send_keys
                if not success:
                    try:
                        self.driver.send_keys(command)
                        log.info(f"✅ 通过driver.send_keys输入成功: {command}")
                        success = True
                    except Exception as e:
                        log.warning(f"driver.send_keys方法异常: {e}")

                if success:
                    # 验证输入是否成功
                    time.sleep(0.5)
                    return self.verify_input_text(command)

            # 方法2: 使用备选输入框
            if self.text_input_box.is_exists():
                log.info("使用备选输入框")

                # 确保输入框获得焦点
                self.text_input_box.click()
                time.sleep(0.5)

                # 尝试多种输入方法
                success = False

                # 尝试1: 使用send_keys方法
                try:
                    if self.text_input_box.send_keys(command):
                        log.info(f"✅ 通过备选输入框send_keys输入成功: {command}")
                        success = True
                except Exception as e:
                    log.warning(f"备选输入框send_keys异常: {e}")

                # 尝试2: 使用driver的send_keys
                if not success:
                    try:
                        self.driver.send_keys(command)
                        log.info(f"✅ 通过driver.send_keys输入成功: {command}")
                        success = True
                    except Exception as e:
                        log.warning(f"driver.send_keys异常: {e}")

                if success:
                    # 验证输入是否成功
                    time.sleep(0.5)
                    return self.verify_input_text(command)

            # 方法3: 使用基于坐标的输入方案（新增）
            log.info("尝试基于坐标的输入方案")
            if self._coordinate_based_input(command):
                return True

            log.error("❌ 所有输入方法都失败")
            return False

        except Exception as e:
            log.error(f"输入文本命令失败: {e}")
            return False

    def clear_input_box(self):
        """清空输入框"""
        try:
            log.info("清空输入框...")

            # 尝试清空主输入框
            if self.input_box.is_exists():
                try:
                    self.input_box.clear_text()
                    log.info("主输入框已清空")
                except:
                    # 如果clear_text失败，尝试选择全部文本然后删除
                    try:
                        self.input_box.click()
                        self.driver.press("ctrl+a")  # 全选
                        self.driver.press("del")     # 删除
                        log.info("通过全选删除清空主输入框")
                    except:
                        log.warning("清空主输入框失败")

            # 尝试清空备选输入框
            if self.text_input_box.is_exists():
                try:
                    self.text_input_box.clear_text()
                    log.info("备选输入框已清空")
                except:
                    log.warning("清空备选输入框失败")

        except Exception as e:
            log.warning(f"清空输入框异常: {e}")

    def verify_input_text(self, expected_text: str) -> bool:
        """
        验证输入的文本是否正确

        Args:
            expected_text: 期望的文本

        Returns:
            bool: 输入是否正确
        """
        try:
            log.info(f"验证输入文本: {expected_text}")

            # 检查主输入框的文本
            if self.input_box.is_exists():
                try:
                    current_text = self.input_box.get_text()
                    if current_text and expected_text.lower() in current_text.lower():
                        log.info(f"✅ 主输入框文本验证成功: {current_text}")
                        return True
                except:
                    log.warning("无法获取主输入框文本")

            # 检查备选输入框的文本
            if self.text_input_box.is_exists():
                try:
                    current_text = self.text_input_box.get_text()
                    if current_text and expected_text.lower() in current_text.lower():
                        log.info(f"✅ 备选输入框文本验证成功: {current_text}")
                        return True
                except:
                    log.warning("无法获取备选输入框文本")

            # 如果无法直接验证，假设输入成功
            log.info("无法直接验证输入文本，假设输入成功")
            return True

        except Exception as e:
            log.warning(f"验证输入文本异常: {e}")
            return True  # 验证失败时假设输入成功
    
    def send_command(self) -> bool:
        """
        发送命令 - 改进版本，支持多种发送方式

        Returns:
            bool: 发送是否成功
        """
        try:
            log.info("发送命令")

            # 方法1: 等待发送按钮可用
            if self.send_button.wait_for_element(timeout=3):
                result = self.send_button.click()
                log.info("使用发送按钮(fl_btn_three_btn)")
                if result:
                    log.info("✅ 命令发送成功")
                    return True
                else:
                    log.warning("发送按钮点击失败，尝试其他方法")
            else:
                log.info("未找到发送按钮，尝试其他发送方法")

            # 方法2: 使用回车键发送
            try:
                log.info("尝试使用回车键发送")
                self.driver.press("enter")
                time.sleep(1)
                log.info("✅ 回车键发送成功")
                return True
            except Exception as e:
                log.warning(f"回车键发送失败: {e}")

            # 方法3: 基于坐标的发送（点击发送区域）
            try:
                log.info("尝试基于坐标的发送")
                screen_width, screen_height = self.driver.window_size()

                # 发送按钮通常在输入框右侧
                send_x = int(screen_width * 0.9)  # 右侧90%位置
                send_y = int(screen_height * 0.9)  # 底部90%位置

                self.driver.click(send_x, send_y)
                time.sleep(1)
                log.info("✅ 基于坐标发送成功")
                return True

            except Exception as e:
                log.warning(f"基于坐标发送失败: {e}")

            # 方法4: 使用Shell命令模拟按键
            try:
                log.info("尝试使用Shell命令发送")
                self.driver.shell("input keyevent 66")  # 66是回车键的keycode
                time.sleep(1)
                log.info("✅ Shell命令发送成功")
                return True

            except Exception as e:
                log.warning(f"Shell命令发送失败: {e}")

            log.error("❌ 所有发送方法都失败")
            return False

        except Exception as e:
            log.error(f"发送命令失败: {e}")
            return False
    
    def execute_text_command(self, command: str) -> bool:
        """
        执行文本命令（输入+发送）- 基于实际脚本
        每次执行前确保回到对话页并打开输入框

        Args:
            command: 要执行的命令

        Returns:
            bool: 执行是否成功
        """
        try:
            log.info(f"执行文本命令: {command}")

            # 1. 确保回到对话页面
            if not self.ensure_on_chat_page():
                log.error("无法确保在对话页面")
                return False

            # 2. 确保输入框可用
            if not self.ensure_input_box_ready():
                log.error("无法确保输入框就绪")
                return False

            # 3. 输入命令
            if not self.input_text_command(command):
                return False

            # 4. 发送命令
            if not self.send_command():
                return False

            log.info("✅ 文本命令执行完成")
            return True

        except Exception as e:
            log.error(f"执行文本命令失败: {e}")
            return False

    def execute_voice_command(self, command: str, duration: float = 3.0, language: str = 'zh-CN') -> bool:
        """
        执行语音命令（优化版本：先检查缓存，再生成语音文件）

        Args:
            command: 要执行的语音命令
            duration: 语音录制持续时间（秒）
            language: 语言代码 (zh-CN, en-US)

        Returns:
            bool: 执行是否成功
        """
        try:
            log.info(f"🎤 执行语音命令: '{command}' (语言: {language}, 持续时间: {duration}秒)")

            # 1. 确保回到对话页面
            if not self.ensure_on_chat_page():
                log.error("无法确保在对话页面")
                return False

            # 2. 确保输入框可用
            if not self.ensure_input_box_ready():
                log.error("无法确保输入框就绪")
                return False

            # 3. 启动语音输入
            if not self.start_voice_input():
                log.warning("无法启动语音输入，回退到文本输入")
                return self.execute_text_command(command)

            # 4. 等待语音录制状态稳定
            log.info("等待语音录制状态稳定...")
            time.sleep(1.0)

            # 5. 检查并播放语音文件（新增功能）
            log.info(f"🔊 播放语音命令: '{command}'")
            voice_success = self._play_voice_command_file(command, language)

            if not voice_success:
                log.warning("语音文件播放失败，回退到文本输入")
                # 先停止语音输入
                self.stop_voice_input()
                return self.execute_text_command(command)

            # 6. 等待语音识别完成
            log.info("等待语音识别完成...")
            time.sleep(2)

            # 7. 停止语音输入
            if not self.stop_voice_input():
                log.warning("无法正常停止语音输入")
                # 尝试按返回键或点击其他区域
                self.driver.press("back")
                time.sleep(0.5)

            # 8. 检查语音识别结果
            recognition_success = self._check_voice_recognition_result()
            if not recognition_success:
                log.info("语音识别可能失败，手动输入文本作为备选")
                if not self.input_text_command(command):
                    return False

            # 9. 发送命令
            if not self.send_command():
                return False

            log.info("✅ 语音命令执行完成")
            return True

        except Exception as e:
            log.error(f"执行语音命令失败: {e}")
            # 回退到文本输入
            log.info("回退到文本输入模式")
            return self.execute_text_command(command)

    def _play_voice_command_file(self, command: str, language: str) -> bool:
        """
        播放语音命令文件（优化版本：优先查找缓存，不存在再生成）

        执行流程:
        1. 检查TTS服务可用性
        2. 优先查找已存在的语音文件缓存
        3. 验证缓存文件完整性
        4. 缓存命中则直接播放，未命中则生成新文件
        5. 播放生成的新文件

        Args:
            command: 语音命令文本
            language: 语言代码

        Returns:
            bool: 播放是否成功
        """
        cache_hit = False
        generation_time = 0
        play_time = 0

        try:
            # 导入TTS工具
            from utils.tts_utils import (
                get_audio_file_path,
                generate_audio_file,
                TTSManager,
                install_tts_dependencies,
                get_tts_info
            )
            from pathlib import Path
            import time

            # 步骤1: 检查TTS服务状态
            tts_info = get_tts_info()
            if not tts_info['selected_service']:
                log.warning("没有可用的TTS服务，尝试安装依赖...")
                if install_tts_dependencies():
                    log.info("✅ TTS依赖安装成功")
                    # 重新获取TTS信息
                    tts_info = get_tts_info()
                    if not tts_info['selected_service']:
                        log.error("TTS服务仍然不可用")
                        return False
                else:
                    log.error("TTS依赖安装失败")
                    return False

            log.debug(f"使用TTS服务: {tts_info['selected_service']}")

            # 步骤2: 获取语音文件路径
            relative_audio_path = get_audio_file_path(command, language, relative=True)
            absolute_audio_path = get_audio_file_path(command, language, relative=False)

            # 步骤3: 优先检查缓存文件是否存在
            audio_file_path = Path(absolute_audio_path)
            tts_manager = TTSManager()

            if audio_file_path.exists():
                log.info(f"🔍 检查缓存文件: {relative_audio_path}")

                # 验证缓存文件完整性
                if tts_manager._verify_audio_file(str(audio_file_path)):
                    file_size = audio_file_path.stat().st_size / 1024  # KB
                    log.info(f"🎯 缓存命中! 使用已存在文件: {relative_audio_path} ({file_size:.1f}KB)")
                    cache_hit = True

                    # 直接播放缓存文件
                    play_start = time.time()
                    success = tts_manager.play_audio_file(str(audio_file_path), volume=0.8)
                    play_time = time.time() - play_start

                    if success:
                        log.info(f"✅ 缓存文件播放成功: '{command}' (播放耗时: {play_time:.2f}秒)")
                        return True
                    else:
                        log.warning(f"⚠️ 缓存文件播放失败，将重新生成")
                        cache_hit = False
                else:
                    log.warning(f"⚠️ 缓存文件验证失败，将重新生成: {relative_audio_path}")
                    cache_hit = False
            else:
                log.info(f"📁 缓存未命中，文件不存在: {relative_audio_path}")
                cache_hit = False

            # 步骤4: 缓存未命中，生成新的语音文件
            if not cache_hit:
                log.info(f"🎵 生成新语音文件: '{command}' ({language})")

                generation_start = time.time()
                generated_path = generate_audio_file(command, language, relative=False)
                generation_time = time.time() - generation_start

                if not generated_path:
                    log.error(f"❌ 语音文件生成失败: '{command}'")
                    return False

                log.info(f"✅ 语音文件生成完成 (生成耗时: {generation_time:.2f}秒)")

                # 步骤5: 播放新生成的文件
                play_start = time.time()
                success = tts_manager.play_audio_file(generated_path, volume=0.8)
                play_time = time.time() - play_start

                if success:
                    total_time = generation_time + play_time
                    log.info(f"✅ 新文件播放成功: '{command}' (播放耗时: {play_time:.2f}秒, 总耗时: {total_time:.2f}秒)")
                    return True
                else:
                    log.error(f"❌ 新生成文件播放失败: '{command}'")
                    return False

        except ImportError as e:
            log.error(f"TTS模块导入失败: {e}")
            return False
        except Exception as e:
            log.error(f"播放语音命令文件异常: {e}")
            return False

    def execute_real_voice_command(self, command: str, language: str = 'zh-CN',
                                  volume: float = 0.8, tts_delay: float = 1.0) -> bool:
        """
        执行真实语音命令（优化版本：优先查找语音文件缓存）

        Args:
            command: 要执行的语音命令
            language: 语言代码 (zh-CN, en-US)
            volume: TTS音量 (0.0-1.0)
            tts_delay: TTS播放前的延迟时间（秒）

        Returns:
            bool: 执行是否成功
        """
        try:
            log.info(f"🎤 执行真实语音命令: '{command}' (语言: {language}, 音量: {volume})")

            # 0. 预检查语音文件缓存状态（提前显示缓存信息）
            self._log_voice_file_cache_status(command, language)

            # 1. 确保回到对话页面
            if not self.ensure_on_chat_page():
                log.error("无法确保在对话页面")
                return False

            # 2. 确保输入框可用
            if not self.ensure_input_box_ready():
                log.error("无法确保输入框就绪")
                return False

            # 3. 启动语音输入
            if not self.start_voice_input():
                log.warning("无法启动语音输入，回退到文本输入")
                return self.execute_text_command(command)

            # 4. 等待语音录制状态稳定
            log.info("等待语音录制状态稳定...")
            time.sleep(tts_delay)

            # 5. 播放语音命令文件（优化版本：优先查找缓存，不存在再生成）
            log.info(f"🔊 播放语音命令: '{command}'")
            start_time = time.time()
            tts_success = self._play_voice_command_file(command, language)
            play_time = time.time() - start_time

            if not tts_success:
                log.warning("语音文件播放失败，回退到文本输入")
                # 先停止语音输入
                self.stop_voice_input()
                return self.execute_text_command(command)

            log.info(f"✅ 语音播放完成，耗时: {play_time:.2f}秒")

            # 6. 等待语音识别完成
            log.info("等待语音识别完成...")
            time.sleep(2)

            # 7. 停止语音输入
            if not self.stop_voice_input():
                log.warning("无法正常停止语音输入")
                # 尝试按返回键或点击其他区域
                self.driver.press("back")
                time.sleep(0.5)

            # 8. 检查语音识别结果
            recognition_success = self._check_voice_recognition_result()
            if not recognition_success:
                log.info("语音识别可能失败，手动输入文本作为备选")
                if not self.input_text_command(command):
                    return False

            # 9. 发送命令
            if not self.send_command():
                return False

            total_time = time.time() - start_time
            log.info(f"✅ 真实语音命令执行完成，总耗时: {total_time:.2f}秒")
            return True

        except Exception as e:
            log.error(f"执行真实语音命令失败: {e}")
            # 回退到文本输入
            log.info("回退到文本输入模式")
            return self.execute_text_command(command)

    def _log_voice_file_cache_status(self, command: str, language: str) -> None:
        """
        记录语音文件缓存状态

        Args:
            command: 语音命令文本
            language: 语言代码
        """
        try:
            from utils.tts_utils import get_audio_file_path
            from pathlib import Path

            # 获取语音文件路径
            relative_path = get_audio_file_path(command, language, relative=True)
            absolute_path = get_audio_file_path(command, language, relative=False)

            # 检查文件是否存在
            audio_file = Path(absolute_path)
            if audio_file.exists():
                file_size = audio_file.stat().st_size / 1024  # KB
                log.info(f"🎯 语音文件缓存状态: 已存在 - {relative_path} ({file_size:.1f}KB)")
            else:
                log.info(f"📁 语音文件缓存状态: 不存在 - {relative_path} (将自动生成)")

        except Exception as e:
            log.debug(f"获取语音文件缓存状态失败: {e}")

    def _play_tts_command(self, command: str, language: str, volume: float) -> bool:
        """
        通过TTS播放语音命令

        Args:
            command: 语音命令文本
            language: 语言代码
            volume: 音量

        Returns:
            bool: TTS播放是否成功
        """
        try:
            # 导入TTS工具
            from utils.tts_utils import speak_text, install_tts_dependencies, get_tts_info

            # 检查TTS服务状态
            tts_info = get_tts_info()
            if not tts_info['selected_service']:
                log.warning("没有可用的TTS服务，尝试安装依赖...")
                if install_tts_dependencies():
                    log.info("✅ TTS依赖安装成功")
                    # 重新获取TTS信息
                    tts_info = get_tts_info()
                    if not tts_info['selected_service']:
                        log.error("TTS服务仍然不可用")
                        return False
                else:
                    log.error("TTS依赖安装失败")
                    return False

            log.info(f"使用TTS服务: {tts_info['selected_service']}")

            # 播放TTS语音
            success = speak_text(command, language, volume)

            if success:
                log.info(f"✅ TTS播放成功: '{command}'")
            else:
                log.error(f"❌ TTS播放失败: '{command}'")

            return success

        except ImportError as e:
            log.error(f"TTS模块导入失败: {e}")
            return False
        except Exception as e:
            log.error(f"TTS播放异常: {e}")
            return False

    def start_voice_input(self) -> bool:
        """
        启动语音输入

        Returns:
            bool: 是否成功启动语音输入
        """
        try:
            log.info("启动语音输入...")

            # 尝试点击语音输入按钮
            voice_buttons = [
                self.voice_input_button,
                self.voice_button_alt,
                # 通过描述查找语音按钮
                self.create_element({"description": "语音输入"}, "语音输入按钮(描述)"),
                self.create_element({"description": "voice"}, "语音按钮(英文描述)"),
                self.create_element({"description": "麦克风"}, "麦克风按钮"),
                # 通过文本查找
                self.create_element({"text": "语音"}, "语音按钮(文本)"),
                # 通过类名查找可能的语音按钮
                self.create_element({"className": "android.widget.ImageButton"}, "图像按钮"),
            ]

            for voice_button in voice_buttons:
                if voice_button.is_exists():
                    log.info(f"找到语音按钮: {voice_button.description}")
                    if voice_button.click():
                        log.info("✅ 语音按钮点击成功")
                        time.sleep(1)

                        # 检查是否进入语音录制状态
                        if self._check_voice_recording_state():
                            log.info("✅ 成功进入语音录制状态")
                            return True
                        else:
                            log.debug("点击后未进入语音录制状态，尝试下一个按钮")
                    else:
                        log.debug(f"语音按钮点击失败: {voice_button.description}")

            # 尝试长按输入框启动语音输入
            log.info("尝试长按输入框启动语音输入...")
            if self.input_box.is_exists():
                self.input_box.long_click()
                time.sleep(1)
                if self._check_voice_recording_state():
                    log.info("✅ 通过长按输入框启动语音输入")
                    return True

            # 尝试通过坐标点击可能的语音按钮区域
            log.info("尝试通过坐标点击语音按钮区域...")
            screen_width, screen_height = self.driver.window_size()

            # 常见的语音按钮位置（输入框右侧）
            voice_positions = [
                (int(screen_width * 0.9), int(screen_height * 0.9)),  # 右下角
                (int(screen_width * 0.85), int(screen_height * 0.9)), # 输入框右侧
                (int(screen_width * 0.5), int(screen_height * 0.95)), # 底部中央
            ]

            for x, y in voice_positions:
                log.info(f"尝试点击坐标 ({x}, {y})")
                self.driver.click(x, y)
                time.sleep(1)
                if self._check_voice_recording_state():
                    log.info(f"✅ 通过坐标 ({x}, {y}) 启动语音输入")
                    return True

            log.warning("❌ 无法启动语音输入")
            return False

        except Exception as e:
            log.error(f"启动语音输入失败: {e}")
            return False

    def stop_voice_input(self) -> bool:
        """
        停止语音输入

        Returns:
            bool: 是否成功停止语音输入
        """
        try:
            log.info("停止语音输入...")

            # 尝试点击停止按钮
            stop_buttons = [
                self.voice_stop_button,
                self.voice_recording_button,
                # 通过描述查找停止按钮
                self.create_element({"description": "停止录制"}, "停止录制按钮"),
                self.create_element({"description": "stop"}, "停止按钮(英文)"),
                self.create_element({"description": "完成"}, "完成按钮"),
                # 通过文本查找
                self.create_element({"text": "停止"}, "停止按钮(文本)"),
                self.create_element({"text": "完成"}, "完成按钮(文本)"),
                self.create_element({"text": "确定"}, "确定按钮"),
            ]

            for stop_button in stop_buttons:
                if stop_button.is_exists():
                    log.info(f"找到停止按钮: {stop_button.description}")
                    if stop_button.click():
                        log.info("✅ 停止按钮点击成功")
                        time.sleep(0.5)
                        return True

            # 尝试再次点击语音按钮来停止录制
            if self.voice_input_button.is_exists():
                log.info("尝试再次点击语音按钮停止录制")
                self.voice_input_button.click()
                time.sleep(0.5)
                return True

            # 尝试点击输入框区域停止录制
            log.info("尝试点击输入框区域停止录制")
            if self.input_box.is_exists():
                self.input_box.click()
                time.sleep(0.5)
                return True

            log.warning("❌ 无法找到停止语音输入的方法")
            return False

        except Exception as e:
            log.error(f"停止语音输入失败: {e}")
            return False

    def _check_voice_recording_state(self) -> bool:
        """
        检查是否处于语音录制状态

        Returns:
            bool: 是否处于语音录制状态
        """
        try:
            # 检查录制状态指示器
            recording_indicators = [
                self.voice_recording_button,
                # 通过描述查找录制状态
                self.create_element({"description": "录制中"}, "录制状态指示器"),
                self.create_element({"description": "recording"}, "录制状态(英文)"),
                # 通过文本查找
                self.create_element({"text": "录制中"}, "录制状态文本"),
                self.create_element({"text": "正在录制"}, "正在录制文本"),
                # 检查可能的录制动画或红点
                self.create_element({"className": "android.widget.ProgressBar"}, "录制进度条"),
            ]

            for indicator in recording_indicators:
                if indicator.is_exists():
                    log.info(f"检测到录制状态指示器: {indicator.description}")
                    return True

            # 检查屏幕是否有录制相关的UI变化
            # 可以通过检查特定颜色、动画等来判断

            return False

        except Exception as e:
            log.debug(f"检查语音录制状态失败: {e}")
            return False

    def _check_voice_recognition_result(self) -> bool:
        """
        检查语音识别结果是否已显示在输入框中

        Returns:
            bool: 是否有语音识别结果
        """
        try:
            # 检查输入框是否有文本内容
            if self.input_box.is_exists():
                input_text = self.input_box.get_text()
                if input_text and input_text.strip():
                    log.info(f"检测到语音识别结果: {input_text}")
                    return True

            if self.text_input_box.is_exists():
                input_text = self.text_input_box.get_text()
                if input_text and input_text.strip():
                    log.info(f"检测到语音识别结果(备选框): {input_text}")
                    return True

            log.info("未检测到语音识别结果")
            return False

        except Exception as e:
            log.debug(f"检查语音识别结果失败: {e}")
            return False

    def ensure_on_chat_page(self) -> bool:
        """
        确保当前在对话页面 - 改进版本，更加健壮

        Returns:
            bool: 是否成功回到对话页面
        """
        try:
            log.info("确保在对话页面...")

            # 首先检查当前进程是否是Ella
            if not self.ensure_ella_process():
                log.warning("当前不在Ella进程，尝试返回Ella")
                if not self.return_to_ella_app():
                    log.error("无法返回Ella应用")
                    return False

            # 使用更宽松的方式检查是否在对话页面
            if self._check_chat_page_indicators():
                log.info("✅ 已在对话页面")
                return True

            # 如果不在对话页面，尝试回到主页
            log.info("不在对话页面，尝试回到主页...")

            # 尝试返回到对话页面
            if self._try_return_to_chat_page():
                log.info("✅ 成功返回到对话页面")
                return True

            # 最后的宽松策略：如果在Ella应用中，就认为成功
            if self.ensure_ella_process():
                log.info("✅ 在Ella应用中，使用宽松策略认为在对话页面")
                return True

            log.error("❌ 无法确保在对话页面")
            return False

        except Exception as e:
            log.error(f"确保在对话页面失败: {e}")
            # 异常情况下的宽松策略
            try:
                if self.ensure_ella_process():
                    log.info("✅ 异常情况下使用宽松策略，在Ella应用中认为成功")
                    return True
            except:
                pass
            return False

    def _check_chat_page_indicators(self) -> bool:
        """
        检查对话页面的多种指示器

        Returns:
            bool: 是否在对话页面
        """
        try:
            # 指示器1: 输入框存在
            try:
                if self.input_box.is_exists() or self.text_input_box.is_exists():
                    log.debug("检测到输入框，在对话页面")
                    return True
            except Exception as e:
                log.debug(f"输入框检测失败: {e}")

            # 指示器2: 发送按钮存在
            try:
                if self.send_button.is_exists():
                    log.debug("检测到发送按钮，在对话页面")
                    return True
            except Exception as e:
                log.debug(f"发送按钮检测失败: {e}")

            # 指示器3: 语音按钮存在
            try:
                if self.voice_input_button.is_exists() or self.voice_button_alt.is_exists():
                    log.debug("检测到语音按钮，在对话页面")
                    return True
            except Exception as e:
                log.debug(f"语音按钮检测失败: {e}")

            # 指示器4: 通用UI元素检测
            try:
                # 检查是否有EditText元素（输入框的通用类型）
                edit_texts = self.driver(className="android.widget.EditText")
                if edit_texts.exists():
                    log.debug("检测到EditText元素，可能在对话页面")
                    return True
            except Exception as e:
                log.debug(f"通用UI元素检测失败: {e}")

            # 指示器5: 检查页面文本内容
            try:
                # 查找可能的对话页面文本
                chat_indicators = ["Hi，我是Ella", "Ella", "输入", "发送", "语音"]
                for indicator in chat_indicators:
                    elements = self.driver(textContains=indicator)
                    if elements.exists():
                        log.debug(f"检测到对话页面文本: {indicator}")
                        return True
            except Exception as e:
                log.debug(f"页面文本检测失败: {e}")

            return False

        except Exception as e:
            log.debug(f"对话页面指示器检测失败: {e}")
            return False

    def ensure_ella_process(self) -> bool:
        """
        确保当前进程是Ella应用

        Returns:
            bool: 当前是否在Ella进程
        """
        try:
            log.info("检查当前进程是否是Ella...")

            # 获取当前前台应用信息
            current_app = self.driver.app_current()
            current_package = current_app.get('package', '')
            current_activity = current_app.get('activity', '')

            log.info(f"当前应用: {current_package}")
            log.info(f"当前Activity: {current_activity}")

            # 检查是否是Ella应用
            ella_packages = [
                "com.transsion.aivoiceassistant",
                "com.transsion.ella"
            ]

            if current_package in ella_packages:
                log.info("✅ 当前在Ella应用进程")
                return True
            else:
                log.warning(f"❌ 当前不在Ella应用进程，当前包名: {current_package}")
                return False

        except Exception as e:
            log.error(f"检查Ella进程失败: {e}")
            return False

    def return_to_ella_app(self) -> bool:
        """
        返回到Ella应用

        Returns:
            bool: 是否成功返回Ella应用
        """
        try:
            log.info("尝试返回Ella应用...")

            # 方法1: 多次按返回键尝试回到Ella
            max_back_attempts = 5
            for i in range(max_back_attempts):
                log.info(f"第{i+1}次按返回键...")
                self.driver.press("back")
                time.sleep(1)

                # 检查是否回到了Ella
                if self.ensure_ella_process():
                    log.info(f"✅ 通过返回键回到Ella应用 (第{i+1}次)")
                    return True

            # 方法2: 通过最近任务切换到Ella
            log.info("尝试通过最近任务切换到Ella...")
            if self._switch_to_ella_via_recent_tasks():
                return True

            # 方法3: 重新启动Ella应用
            log.info("尝试重新启动Ella应用...")
            if self.start_app_with_activity():
                if self.wait_for_page_load(timeout=10):
                    log.info("✅ 重新启动Ella应用成功")
                    return True

            log.error("❌ 无法返回Ella应用")
            return False

        except Exception as e:
            log.error(f"返回Ella应用失败: {e}")
            return False

    def _switch_to_ella_via_recent_tasks(self) -> bool:
        """
        通过最近任务切换到Ella应用

        Returns:
            bool: 是否成功切换
        """
        try:
            log.info("通过最近任务切换到Ella...")

            # 打开最近任务
            self.driver.press("recent")
            time.sleep(2)

            # 查找Ella应用的任务卡片
            ella_task_selectors = [
                self.driver(text="Ella"),
                self.driver(text="语音助手"),
                self.driver(text="AI语音助手"),
                self.driver(description="Ella"),
                self.driver(description="语音助手"),
                # 通过包名查找
                self.driver(resourceId="com.transsion.aivoiceassistant:id/app_name"),
            ]

            for selector in ella_task_selectors:
                if selector.exists():
                    log.info("找到Ella任务卡片，点击切换")
                    selector.click()
                    time.sleep(2)

                    # 检查是否成功切换到Ella
                    if self.ensure_ella_process():
                        log.info("✅ 通过最近任务成功切换到Ella")
                        return True

            # 如果没找到特定的Ella卡片，尝试点击第一个任务卡片
            log.info("未找到Ella特定卡片，尝试点击任务卡片...")
            task_cards = self.driver(className="android.widget.FrameLayout")
            if task_cards.exists():
                for i in range(min(3, task_cards.count)):  # 最多尝试前3个任务
                    try:
                        card = task_cards[i] if task_cards.count > 1 else task_cards
                        card.click()
                        time.sleep(2)

                        if self.ensure_ella_process():
                            log.info(f"✅ 通过第{i+1}个任务卡片切换到Ella")
                            return True
                        else:
                            # 如果不是Ella，继续尝试下一个
                            self.driver.press("recent")
                            time.sleep(1)
                    except Exception as e:
                        log.debug(f"点击第{i+1}个任务卡片失败: {e}")
                        continue

            # 关闭最近任务界面
            self.driver.press("back")
            time.sleep(1)

            return False

        except Exception as e:
            log.error(f"通过最近任务切换失败: {e}")
            return False

    def _try_return_to_chat_page(self) -> bool:
        """
        尝试返回到对话页面的多种方法

        Returns:
            bool: 是否成功返回对话页面
        """
        try:
            # 方法1: 按返回键回到主页
            log.info("方法1: 按返回键回到主页...")
            self.driver.press("back")
            time.sleep(1)

            # 检查是否回到了对话页面
            if self._check_chat_page_indicators():
                log.info("✅ 通过返回键回到对话页面")
                return True

            # 方法2: 尝试点击主页按钮（如果存在）
            log.info("方法2: 尝试点击主页按钮...")
            home_buttons = [
                self.driver(resourceId="com.transsion.aivoiceassistant:id/iv_home"),
                self.driver(resourceId="com.transsion.aivoiceassistant:id/btn_home"),
                self.driver(text="主页"),
                self.driver(text="Home"),
                self.driver(description="主页"),
                self.driver(description="Home")
            ]

            for home_button in home_buttons:
                if home_button.exists():
                    log.info("找到主页按钮，点击回到主页")
                    home_button.click()
                    time.sleep(1)

                    # 检查是否回到了对话页面
                    if self._check_chat_page_indicators():
                        log.info("✅ 通过主页按钮回到对话页面")
                        return True

            # 方法3: 尝试多次返回键
            log.info("方法3: 尝试多次返回键...")
            for i in range(3):
                self.driver.press("back")
                time.sleep(1)

                if self._check_chat_page_indicators():
                    log.info(f"✅ 通过第{i+1}次返回键回到对话页面")
                    return True

            # 方法4: 重新启动应用到主页
            log.info("方法4: 重新启动应用到主页...")
            if self.start_app_with_activity():
                if self.wait_for_page_load(timeout=10):
                    log.info("✅ 重新启动应用成功")
                    return True

            log.error("❌ 无法回到对话页面")
            return False

        except Exception as e:
            log.error(f"尝试返回对话页面失败: {e}")
            return False

    def ensure_input_box_ready(self) -> bool:
        """
        确保输入框就绪可用 - 改进版本，使用多种检测策略

        Returns:
            bool: 输入框是否就绪
        """
        try:
            log.info("确保输入框就绪...")

            # 策略1: 检查已知的输入框元素
            if self._check_known_input_elements():
                return True

            # 策略2: 通用输入框检测
            if self._check_generic_input_elements():
                return True

            # 策略3: 通过坐标激活输入区域
            if self._activate_input_by_coordinates():
                return True

            # 策略4: 通过滑动显示输入框
            if self._activate_input_by_scrolling():
                return True

            # 策略5: 宽松策略 - 检查是否有任何可交互元素
            if self._check_any_interactive_elements():
                log.info("✅ 检测到可交互元素，假设输入功能可用")
                return True

            log.error("❌ 无法确保输入框就绪")
            return False

        except Exception as e:
            log.error(f"确保输入框就绪失败: {e}")
            return False

    def _check_known_input_elements(self) -> bool:
        """检查已知的输入框元素"""
        try:
            log.info("检查已知输入框元素...")

            # 检查主输入框
            if self.input_box.is_exists():
                log.info("找到主输入框")
                if self.input_box.click():
                    log.info("✅ 主输入框已激活")
                    time.sleep(0.5)
                    return True

            # 检查备选输入框
            if self.text_input_box.is_exists():
                log.info("找到备选输入框")
                if self.text_input_box.click():
                    log.info("✅ 备选输入框已激活")
                    time.sleep(0.5)
                    return True

            return False

        except Exception as e:
            log.debug(f"检查已知输入框元素失败: {e}")
            return False

    def _check_generic_input_elements(self) -> bool:
        """检查通用输入框元素"""
        try:
            log.info("检查通用输入框元素...")

            # 查找所有EditText元素
            edit_texts = self.driver(className="android.widget.EditText")
            if edit_texts.exists():
                log.info(f"找到 {edit_texts.count} 个EditText元素")

                # 尝试点击第一个EditText
                first_edit_text = edit_texts[0] if edit_texts.count > 1 else edit_texts
                if first_edit_text.click():
                    log.info("✅ 通用输入框已激活")
                    time.sleep(0.5)
                    return True

            # 查找可能的输入相关元素
            input_hints = ["输入", "搜索", "请输入", "input", "search"]
            for hint in input_hints:
                elements = self.driver(textContains=hint)
                if elements.exists():
                    log.info(f"找到包含'{hint}'的元素")
                    if elements.click():
                        log.info(f"✅ 通过'{hint}'激活输入框")
                        time.sleep(0.5)
                        return True

            return False

        except Exception as e:
            log.debug(f"检查通用输入框元素失败: {e}")
            return False

    def _activate_input_by_coordinates(self) -> bool:
        """通过坐标激活输入区域"""
        try:
            log.info("尝试通过坐标激活输入区域...")

            screen_width, screen_height = self.driver.window_size()

            # 尝试多个可能的输入框位置
            input_positions = [
                (screen_width // 2, int(screen_height * 0.9)),   # 底部中央
                (screen_width // 2, int(screen_height * 0.85)),  # 底部偏上
                (screen_width // 2, int(screen_height * 0.8)),   # 中下部
                (int(screen_width * 0.8), int(screen_height * 0.9)),  # 底部右侧
            ]

            for x, y in input_positions:
                log.info(f"尝试点击坐标 ({x}, {y})")
                self.driver.click(x, y)
                time.sleep(1)

                # 检查是否激活了输入框
                if self._verify_input_activated():
                    log.info(f"✅ 通过坐标 ({x}, {y}) 激活了输入框")
                    return True

            return False

        except Exception as e:
            log.debug(f"通过坐标激活输入失败: {e}")
            return False

    def _activate_input_by_scrolling(self) -> bool:
        """通过滑动激活输入框"""
        try:
            log.info("尝试通过滑动激活输入框...")

            screen_width, screen_height = self.driver.window_size()

            # 向上滑动显示底部输入框
            self.driver.swipe(
                screen_width // 2,
                int(screen_height * 0.7),
                screen_width // 2,
                int(screen_height * 0.3),
                duration=0.5
            )
            time.sleep(1)

            # 检查是否出现了输入框
            if self._verify_input_activated():
                log.info("✅ 通过滑动激活了输入框")
                return True

            # 向下滑动尝试
            self.driver.swipe(
                screen_width // 2,
                int(screen_height * 0.3),
                screen_width // 2,
                int(screen_height * 0.7),
                duration=0.5
            )
            time.sleep(1)

            if self._verify_input_activated():
                log.info("✅ 通过向下滑动激活了输入框")
                return True

            return False

        except Exception as e:
            log.debug(f"通过滑动激活输入失败: {e}")
            return False

    def _check_any_interactive_elements(self) -> bool:
        """检查是否有任何可交互元素"""
        try:
            log.info("检查可交互元素...")

            # 检查是否有按钮
            buttons = self.driver(className="android.widget.Button")
            if buttons.exists():
                log.info(f"找到 {buttons.count} 个按钮元素")
                return True

            # 检查是否有图像按钮
            image_buttons = self.driver(className="android.widget.ImageButton")
            if image_buttons.exists():
                log.info(f"找到 {image_buttons.count} 个图像按钮元素")
                return True

            # 检查是否有文本视图（表示页面有内容）
            text_views = self.driver(className="android.widget.TextView")
            if text_views.exists() and text_views.count > 3:
                log.info(f"找到 {text_views.count} 个文本元素，页面内容丰富")
                return True

            # 新增：基于应用状态的宽松检测
            current_app = self.driver.app_current()
            if current_app.get('package') == 'com.transsion.aivoiceassistant':
                log.info("✅ Ella应用在前台，使用基于坐标的交互方案")
                return self._test_coordinate_interaction()

            return False

        except Exception as e:
            log.debug(f"检查可交互元素失败: {e}")
            return False

    def _test_coordinate_interaction(self) -> bool:
        """测试基于坐标的交互（发现的可交互区域）"""
        try:
            log.info("测试已发现的可交互区域...")

            screen_width, screen_height = self.driver.window_size()

            # 使用发现的可交互坐标 (540, 2160)
            # 但需要根据当前屏幕尺寸进行调整
            if screen_height == 2400:
                # 标准尺寸，使用发现的坐标
                test_x, test_y = 540, 2160
            else:
                # 按比例调整坐标
                test_x = screen_width // 2
                test_y = int(screen_height * 0.9)  # 90%位置

            log.info(f"测试坐标: ({test_x}, {test_y})")

            # 点击测试区域
            self.driver.click(test_x, test_y)
            time.sleep(1)

            # 尝试输入测试文本
            try:
                self.driver.send_keys("test")
                time.sleep(0.5)

                # 清除测试文本
                self.driver.press("ctrl+a")
                self.driver.press("del")

                log.info("✅ 基于坐标的交互测试成功")
                return True

            except Exception as e:
                log.debug(f"坐标交互测试失败: {e}")
                return False

        except Exception as e:
            log.debug(f"测试坐标交互失败: {e}")
            return False

    def _coordinate_based_input(self, command: str) -> bool:
        """基于坐标的文本输入方案"""
        try:
            log.info(f"使用基于坐标的输入方案: {command}")

            screen_width, screen_height = self.driver.window_size()

            # 使用发现的可交互坐标，根据屏幕尺寸调整
            if screen_height == 2400:
                # 标准尺寸，使用发现的坐标
                input_x, input_y = 540, 2160
            else:
                # 按比例调整坐标
                input_x = screen_width // 2
                input_y = int(screen_height * 0.9)

            log.info(f"点击输入区域坐标: ({input_x}, {input_y})")

            # 1. 点击输入区域激活
            self.driver.click(input_x, input_y)
            time.sleep(1)

            # 2. 清空可能存在的文本
            try:
                self.driver.press("ctrl+a")
                self.driver.press("del")
                time.sleep(0.5)
            except:
                pass

            # 3. 输入命令文本
            try:
                self.driver.send_keys(command)
                time.sleep(1)
                log.info(f"✅ 基于坐标输入成功: {command}")
                return True

            except Exception as e:
                log.warning(f"基于坐标输入失败: {e}")

                # 备选方案：使用Shell命令输入
                try:
                    log.info("尝试使用Shell命令输入")
                    escaped_command = command.replace("'", "\\'")
                    self.driver.shell(f"input text '{escaped_command}'")
                    time.sleep(1)
                    log.info(f"✅ Shell命令输入成功: {command}")
                    return True

                except Exception as e2:
                    log.error(f"Shell命令输入也失败: {e2}")
                    return False

        except Exception as e:
            log.error(f"基于坐标的输入方案失败: {e}")
            return False

    def _verify_input_activated(self) -> bool:
        """验证输入框是否已激活"""
        try:
            # 检查已知输入框是否出现
            if (self.input_box.is_exists() or
                self.text_input_box.is_exists()):
                return True

            # 检查是否有EditText元素
            edit_texts = self.driver(className="android.widget.EditText")
            if edit_texts.exists():
                return True

            # 检查是否有软键盘出现（间接表示输入框激活）
            # 这里可以添加软键盘检测逻辑

            return False

        except Exception as e:
            log.debug(f"验证输入激活状态失败: {e}")
            return False
            log.error(f"确保输入框就绪失败: {e}")
            return False

    def verify_tts_response(self, timeout: int = 10) -> bool:
        """
        验证TTS响应 - 基于实际脚本

        Args:
            timeout: 等待超时时间

        Returns:
            bool: 是否检测到TTS播放按钮
        """
        try:
            log.info(f"验证TTS响应，等待TTS播放按钮出现")

            # 直接等待TTS播放按钮出现（替代固定等待时间）
            if self.tts_play_button.wait_for_element(timeout):
                log.info("✅ 检测到TTS播放按钮，响应正常")

                # 可选：点击TTS播放按钮
                if self.tts_play_button.click():
                    log.info("✅ TTS播放按钮点击成功")
                else:
                    log.warning("⚠️ TTS播放按钮点击失败")

                return True
            else:
                log.warning("⚠️ 未检测到TTS播放按钮")
                return False

        except Exception as e:
            log.error(f"验证TTS响应失败: {e}")
            return False
    
    def wait_for_response(self, timeout: int = 10) -> bool:
        """
        等待AI响应 - 优化版本，快速响应检测

        Args:
            timeout: 等待超时时间

        Returns:
            bool: 是否收到响应
        """
        try:
            log.info(f"快速等待AI响应，超时时间: {timeout}秒")

            # 首先确保在Ella应用（简化检查）
            if not self.ensure_ella_process():
                log.warning("等待响应时发现不在Ella进程，尝试返回")
                if not self.return_to_ella_app():
                    log.error("无法返回Ella应用")
                    return False

            start_time = time.time()
            check_interval = 0.3  # 减少检查间隔，提高响应速度
            last_process_check = 0

            # 使用更快的初始检测方法
            initial_element_count = self._get_quick_element_count()
            log.info(f"初始元素数量: {initial_element_count}")

            while time.time() - start_time < timeout:
                current_time = time.time()

                # 减少进程检查频率到10秒一次
                if current_time - last_process_check > 10:
                    if not self.ensure_ella_process():
                        log.warning("等待响应期间检测到离开了Ella进程，尝试返回")
                        if self.return_to_ella_app():
                            self.ensure_on_chat_page()
                        else:
                            log.error("等待响应期间无法返回Ella应用")
                            return False
                    last_process_check = current_time

                # 快速检查响应
                if self._quick_check_for_response(initial_element_count):
                    log.info("✅ 快速检测到AI响应")
                    return True

                # 短暂等待
                time.sleep(check_interval)

            log.warning("快速等待AI响应超时")
            return False

        except Exception as e:
            log.error(f"快速等待AI响应失败: {e}")
            return False

    def _get_quick_element_count(self) -> int:
        """
        快速获取页面元素数量

        Returns:
            int: 页面TextView元素数量
        """
        try:
            text_views = self.driver(className="android.widget.TextView")
            if text_views.exists():
                return text_views.count
            return 0
        except Exception as e:
            log.debug(f"快速获取元素数量失败: {e}")
            return 0

    def _quick_check_for_response(self, initial_count: int) -> bool:
        """
        快速检查是否有新响应

        Args:
            initial_count: 初始元素数量

        Returns:
            bool: 是否检测到新响应
        """
        try:
            # 方法1: 检查元素数量变化
            current_count = self._get_quick_element_count()
            if current_count > initial_count:
                log.info(f"检测到元素数量增加: {current_count} > {initial_count}")

                # 进一步验证是否是AI响应
                if self._verify_ai_response_exists():
                    return True

            # 方法2: 直接检查TTS按钮
            if self._check_tts_button_appeared():
                log.info("检测到TTS按钮，表示有AI响应")
                return True

            # 方法3: 检查特定的响应元素
            if self._check_response_elements():
                log.info("检测到响应相关元素")
                return True

            return False

        except Exception as e:
            log.debug(f"快速检查响应失败: {e}")
            return False

    def _verify_ai_response_exists(self) -> bool:
        """
        验证是否存在AI响应（快速版本）

        Returns:
            bool: 是否存在AI响应
        """
        try:
            # 只检查最新的几个文本元素
            text_views = self.driver(className="android.widget.TextView")
            if not text_views.exists():
                return False

            # 只检查最后5个元素，提高速度
            count = min(text_views.count, 5)
            for i in range(max(0, text_views.count - count), text_views.count):
                try:
                    tv = text_views[i] if text_views.count > 1 else text_views
                    text = tv.get_text()
                    if text and self._is_ai_response(text.strip()):
                        log.info(f"快速验证找到AI响应: {text.strip()}")
                        return True
                except:
                    continue

            return False

        except Exception as e:
            log.debug(f"快速验证AI响应失败: {e}")
            return False

    def _check_response_elements(self) -> bool:
        """
        检查响应相关的UI元素

        Returns:
            bool: 是否找到响应元素
        """
        try:
            # 检查常见的响应相关元素
            response_indicators = [
                # 蓝牙相关文本
                self.driver(textContains="蓝牙"),
                self.driver(textContains="bluetooth"),
                self.driver(textContains="已打开"),
                self.driver(textContains="已关闭"),
                # 常见AI响应词汇
                self.driver(textContains="成功"),
                self.driver(textContains="完成"),
                self.driver(textContains="已"),
            ]

            for indicator in response_indicators:
                if indicator.exists():
                    # 进一步验证这个文本是否是新出现的AI响应
                    try:
                        text = indicator.get_text()
                        if text and self._is_ai_response(text.strip()):
                            log.info(f"通过元素检查找到AI响应: {text.strip()}")
                            return True
                    except:
                        continue

            return False

        except Exception as e:
            log.debug(f"检查响应元素失败: {e}")
            return False

    def _get_page_text_snapshot(self) -> str:
        """
        获取页面文本快照

        Returns:
            str: 页面所有文本的快照
        """
        try:
            all_texts = []
            text_views = self.driver(className="android.widget.TextView")

            if text_views.exists():
                for i in range(text_views.count):
                    try:
                        tv = text_views[i] if text_views.count > 1 else text_views
                        text = tv.get_text()
                        if text and len(text.strip()) > 0:
                            all_texts.append(text.strip())
                    except:
                        continue

            # 返回所有文本的连接字符串
            return "\n".join(all_texts)

        except Exception as e:
            log.debug(f"获取页面文本快照失败: {e}")
            return ""

    def _check_for_new_response(self, initial_snapshot: str) -> bool:
        """
        检查是否有新的AI响应

        Args:
            initial_snapshot: 初始页面文本快照

        Returns:
            bool: 是否检测到新响应
        """
        try:
            # 获取当前页面文本快照
            current_snapshot = self._get_page_text_snapshot()

            # 如果页面内容没有变化，说明还没有响应
            if current_snapshot == initial_snapshot:
                return False

            # 计算新增的文本内容
            initial_lines = set(initial_snapshot.split('\n'))
            current_lines = set(current_snapshot.split('\n'))
            new_lines = current_lines - initial_lines

            if not new_lines:
                return False

            log.info(f"检测到新增文本行数: {len(new_lines)}")

            # 检查新增文本是否包含AI响应特征
            for new_line in new_lines:
                if self._is_ai_response(new_line):
                    log.info(f"检测到AI响应: {new_line}")
                    return True

            # 检查是否有TTS播放按钮出现（表示有语音响应）
            if self._check_tts_button_appeared():
                log.info("检测到TTS按钮出现，表示有AI响应")
                return True

            # 检查页面是否有明显的内容增加
            if len(current_snapshot) > len(initial_snapshot) + 50:  # 内容增加超过50个字符
                log.info(f"页面内容显著增加: {len(current_snapshot)} > {len(initial_snapshot)}")
                return True

            return False

        except Exception as e:
            log.debug(f"检查新响应失败: {e}")
            return False

    def _is_ai_response(self, text: str) -> bool:
        """
        判断文本是否是AI响应

        Args:
            text: 要检查的文本

        Returns:
            bool: 是否是AI响应
        """
        try:
            if not text or len(text.strip()) < 2:
                return False

            text = text.strip()
            text_lower = text.lower()

            # 过滤掉用户输入的命令
            user_commands = ["open bluetooth", "close bluetooth", "open contacts", "what time", "hello"]
            if any(cmd in text_lower for cmd in user_commands):
                return False

            # 过滤掉固定的UI文本
            ui_texts = [
                "hi，我是ella", "我可以为你", "换一换", "有问题尽管问我",
                "昨天", "今天", ":", "ag600", "足球表面", "中欧班列", "deepseek"
            ]
            if any(ui_text in text_lower for ui_text in ui_texts):
                return False

            # 检查蓝牙相关响应（优先级最高）
            bluetooth_patterns = [
                "蓝牙.*已.*打开", "蓝牙.*已.*开启", "蓝牙.*已.*关闭",
                "bluetooth.*on", "bluetooth.*off", "bluetooth.*enabled", "bluetooth.*disabled",
                "蓝牙 已打开", "蓝牙已打开", "蓝牙 已关闭", "蓝牙已关闭"
            ]

            # 检查联系人相关响应
            contacts_patterns = [
                "联系人.*已.*打开", "联系人.*已.*开启", "通讯录.*已.*打开", "通讯录.*已.*开启",
                "contacts.*opened", "contacts.*launched", "contact.*app.*opened",
                "联系人 已打开", "联系人已打开", "通讯录 已打开", "通讯录已打开",
                "正在打开联系人", "正在打开通讯录", "opening contacts"
            ]

            import re
            for pattern in bluetooth_patterns:
                if re.search(pattern, text_lower):
                    log.info(f"匹配到蓝牙响应模式: {pattern} -> {text}")
                    return True

            for pattern in contacts_patterns:
                if re.search(pattern, text_lower):
                    log.info(f"匹配到联系人响应模式: {pattern} -> {text}")
                    return True

            # 检查AI响应的常见特征
            ai_indicators = [
                "已", "成功", "失败", "正在", "无法", "请", "您好", "抱歉",
                "我", "为您", "帮您", "可以", "不能", "设置", "打开", "关闭"
            ]

            # 至少包含一个AI指示词且长度合适
            if any(indicator in text for indicator in ai_indicators) and len(text) >= 3:
                # 排除太短的文本（除非是明确的状态响应）
                if len(text) >= 5 or any(keyword in text_lower for keyword in ["蓝牙", "bluetooth", "联系人", "通讯录", "contacts"]):
                    log.info(f"匹配到AI响应特征: {text}")
                    return True

            return False

        except Exception as e:
            log.debug(f"判断AI响应失败: {e}")
            return False

    def _check_tts_button_appeared(self) -> bool:
        """
        检查TTS播放按钮是否出现

        Returns:
            bool: TTS按钮是否出现
        """
        try:
            # 检查TTS播放按钮
            if self.tts_play_button.is_exists():
                log.info("检测到TTS播放按钮")
                return True

            # 检查其他可能的播放按钮
            play_buttons = [
                self.driver(resourceId="com.transsion.aivoiceassistant:id/iv_play"),
                self.driver(resourceId="com.transsion.aivoiceassistant:id/btn_play"),
                self.driver(description="播放"),
                self.driver(description="play"),
                self.driver(className="android.widget.ImageButton")
            ]

            for button in play_buttons:
                if button.exists():
                    log.info("检测到播放相关按钮")
                    return True

            return False

        except Exception as e:
            log.debug(f"检查TTS按钮失败: {e}")
            return False

    def smart_wait_with_process_check(self, operation_func, timeout: int = 10, check_interval: float = 2.0):
        """
        智能等待装饰器，在等待期间检查进程状态

        Args:
            operation_func: 要执行的操作函数
            timeout: 超时时间
            check_interval: 进程检查间隔

        Returns:
            操作函数的返回值
        """
        try:
            log.info(f"开始智能等待操作，超时: {timeout}秒")

            start_time = time.time()
            last_process_check = 0

            while time.time() - start_time < timeout:
                # 定期检查进程状态
                current_time = time.time()
                if current_time - last_process_check > check_interval:
                    if not self.ensure_ella_process():
                        log.warning("智能等待期间检测到离开了Ella进程，尝试返回")
                        if self.return_to_ella_app():
                            self.ensure_on_chat_page()
                        else:
                            log.error("智能等待期间无法返回Ella应用")
                            return False
                    last_process_check = current_time

                # 执行操作函数
                try:
                    result = operation_func()
                    if result:  # 如果操作成功，返回结果
                        log.info("✅ 智能等待操作成功")
                        return result
                except Exception as e:
                    log.debug(f"操作函数执行异常: {e}")

                # 短暂等待后继续
                time.sleep(0.5)

            log.warning("智能等待操作超时")
            return False

        except Exception as e:
            log.error(f"智能等待操作失败: {e}")
            return False

    def check_bluetooth_status_smart(self) -> bool:
        """
        智能检查蓝牙状态 - 包含进程检测

        Returns:
            bool: 蓝牙是否开启
        """
        try:
            log.info("智能检查蓝牙状态...")

            # 确保在Ella进程
            if not self.ensure_ella_process():
                log.warning("检查蓝牙状态时不在Ella进程，尝试返回")
                if not self.return_to_ella_app():
                    log.error("无法返回Ella应用")
                    return False

            # 调用原有的蓝牙状态检查方法
            return self.check_bluetooth_status()

        except Exception as e:
            log.error(f"智能检查蓝牙状态失败: {e}")
            return False

    def get_response_text_smart(self) -> str:
        """
        智能获取响应文本 - 包含进程检测

        Returns:
            str: 响应文本
        """
        try:
            log.info("智能获取响应文本...")

            # 确保在Ella进程
            if not self.ensure_ella_process():
                log.warning("获取响应文本时不在Ella进程，尝试返回")
                if not self.return_to_ella_app():
                    log.error("无法返回Ella应用")
                    return ""

            # 调用原有的获取响应文本方法
            return self.get_response_text()

        except Exception as e:
            log.error(f"智能获取响应文本失败: {e}")
            return ""
    
    def get_response_text(self) -> str:
        """
        获取AI响应文本 - 修复版本

        Returns:
            str: 响应文本内容
        """
        try:
            log.info("获取AI响应文本")

            # 获取所有文本视图
            all_texts = []
            text_views = self.driver(className="android.widget.TextView")

            if text_views.exists():
                for i in range(text_views.count):
                    try:
                        tv = text_views[i] if text_views.count > 1 else text_views
                        text = tv.get_text()
                        if text and len(text.strip()) > 0:
                            all_texts.append(text.strip())
                    except:
                        continue

            log.info(f"页面上所有文本元素数量: {len(all_texts)}")

            # 查找AI响应
            ai_responses = []

            for text in all_texts:
                if self._is_ai_response(text):
                    ai_responses.append(text)
                    log.info(f"找到AI响应: {text}")

            # 返回最相关的响应
            if ai_responses:
                # 优先返回蓝牙相关响应
                bluetooth_responses = []
                for response in ai_responses:
                    if any(keyword in response.lower() for keyword in ["蓝牙", "bluetooth"]):
                        bluetooth_responses.append(response)

                if bluetooth_responses:
                    # 返回最长的蓝牙响应
                    best_response = max(bluetooth_responses, key=len)
                    log.info(f"获取到蓝牙相关响应: {best_response}")
                    return best_response

                # 返回最长的AI响应
                best_response = max(ai_responses, key=len)
                log.info(f"获取到AI响应: {best_response}")
                return best_response

            # 如果没有找到明确的AI响应，尝试查找最新的有意义文本
            meaningful_texts = []
            for text in all_texts:
                # 过滤掉明显的UI文本
                if self._is_meaningful_text(text):
                    meaningful_texts.append(text)

            if meaningful_texts:
                # 返回最后一个有意义的文本（可能是最新的响应）
                latest_text = meaningful_texts[-1]
                log.info(f"获取到最新有意义文本: {latest_text}")
                return latest_text

            log.warning("未找到合适的响应文本")
            return ""

        except Exception as e:
            log.error(f"获取响应文本失败: {e}")
            return ""

    def _is_meaningful_text(self, text: str) -> bool:
        """
        判断文本是否有意义（不是UI固定文本）

        Args:
            text: 要检查的文本

        Returns:
            bool: 是否是有意义的文本
        """
        try:
            if not text or len(text.strip()) < 3:
                return False

            text = text.strip()
            text_lower = text.lower()

            # 过滤掉固定的UI文本
            ui_texts = [
                "hi，我是ella", "我可以为你答疑解惑", "换一换", "有问题尽管问我",
                "ag600获生产许可", "足球表面有多少块皮革", "中欧班列开行超11万列",
                "deepseek-r1", "昨天", "今天"
            ]

            for ui_text in ui_texts:
                if ui_text in text_lower:
                    return False

            # 过滤掉时间戳
            if ":" in text and len(text) < 10:
                return False

            # 过滤掉用户输入的命令
            user_commands = ["open bluetooth", "close bluetooth", "what time", "hello"]
            if any(cmd in text_lower for cmd in user_commands):
                return False

            # 如果文本长度合适且不在过滤列表中，认为是有意义的
            return len(text) >= 3

        except Exception as e:
            log.debug(f"判断有意义文本失败: {e}")
            return False
    
    def check_bluetooth_status(self) -> bool:
        """
        检查蓝牙状态
        
        Returns:
            bool: 蓝牙是否已开启
        """
        try:
            log.info("检查蓝牙状态")
            
            # 通过ADB命令检查蓝牙状态
            import subprocess
            result = subprocess.run(
                ["adb", "shell", "settings", "get", "global", "bluetooth_on"],
                capture_output=True,
                text=True,
                timeout=5
            )
            
            if result.returncode == 0:
                bluetooth_status = result.stdout.strip()
                is_on = bluetooth_status == "1"
                log.info(f"蓝牙状态: {'开启' if is_on else '关闭'} (值: {bluetooth_status})")
                return is_on
            else:
                log.error(f"获取蓝牙状态失败: {result.stderr}")
                return False
                
        except Exception as e:
            log.error(f"检查蓝牙状态失败: {e}")
            return False
    
    def verify_command_in_response(self, command: str, response: str) -> bool:
        """
        验证响应中是否包含命令内容

        Args:
            command: 原始命令
            response: AI响应

        Returns:
            bool: 响应是否包含命令相关内容
        """
        try:
            log.info(f"验证响应是否包含命令: {command}")

            if not response:
                log.warning("响应为空")
                return False

            # 转换为小写进行比较
            command_lower = command.lower()
            response_lower = response.lower()

            # 检查是否包含完整命令
            if command_lower in response_lower:
                log.info(f"✅ 响应包含完整命令: {command}")
                return True

            # 特殊处理蓝牙命令
            if "bluetooth" in command_lower:
                # 蓝牙相关的响应关键词
                bluetooth_response_keywords = [
                    "蓝牙", "bluetooth", "已打开", "已关闭", "已开启", "已关闭",
                    "打开", "关闭", "开启", "设置"
                ]

                # 检查响应是否包含蓝牙相关关键词
                found_bluetooth_keywords = []
                for keyword in bluetooth_response_keywords:
                    if keyword in response_lower:
                        found_bluetooth_keywords.append(keyword)

                if found_bluetooth_keywords:
                    log.info(f"✅ 响应包含蓝牙相关关键词: {found_bluetooth_keywords}")
                    return True

            # 特殊处理闹钟命令
            if "alarm" in command_lower or "闹钟" in command_lower:
                # 闹钟相关的响应关键词
                alarm_response_keywords = [
                    "闹钟", "alarm", "设置", "set", "提醒", "reminder",
                    "已设置", "设定", "添加", "创建", "时间", "明天"
                ]

                # 检查响应是否包含闹钟相关关键词
                found_alarm_keywords = []
                for keyword in alarm_response_keywords:
                    if keyword in response_lower:
                        found_alarm_keywords.append(keyword)

                if found_alarm_keywords:
                    log.info(f"✅ 响应包含闹钟相关关键词: {found_alarm_keywords}")
                    return True

            # 特殊处理天气命令
            if "weather" in command_lower or "天气" in command_lower:
                # 天气相关的响应关键词
                weather_response_keywords = [
                    "weather", "天气", "temperature", "温度", "rain", "雨",
                    "sunny", "晴", "cloudy", "云", "wind", "风", "humidity", "湿度",
                    "forecast", "预报", "tomorrow", "明天", "today", "今天",
                    "shanghai", "上海", "celsius", "摄氏度", "fahrenheit", "华氏度",
                    "degree", "度", "°c", "°f", "precipitation", "降水"
                ]

                # 检查响应是否包含天气相关关键词
                found_weather_keywords = []
                for keyword in weather_response_keywords:
                    if keyword in response_lower:
                        found_weather_keywords.append(keyword)

                if found_weather_keywords:
                    log.info(f"✅ 响应包含天气相关关键词: {found_weather_keywords}")
                    return True

            # 特殊处理拍照命令
            if "photo" in command_lower or "picture" in command_lower or "拍照" in command_lower or "照片" in command_lower:
                # 拍照相关的响应关键词
                photo_response_keywords = [
                    "photo", "拍照", "picture", "照片", "camera", "相机", "摄像头",
                    "take", "拍", "capture", "捕获", "shot", "拍摄", "snap", "快照",
                    "image", "图像", "pic", "图片", "photograph", "摄影"
                ]

                # 检查响应是否包含拍照相关关键词
                found_photo_keywords = []
                for keyword in photo_response_keywords:
                    if keyword in response_lower:
                        found_photo_keywords.append(keyword)

                if found_photo_keywords:
                    log.info(f"✅ 响应包含拍照相关关键词: {found_photo_keywords}")
                    return True

            # 检查是否包含命令关键词
            keywords = command_lower.split()
            found_keywords = []

            for keyword in keywords:
                if keyword in response_lower:
                    found_keywords.append(keyword)

            if len(found_keywords) >= len(keywords) * 0.5:  # 至少包含50%的关键词
                log.info(f"✅ 响应包含关键词: {found_keywords}")
                return True
            else:
                log.warning(f"⚠️ 响应包含的关键词不足: {found_keywords}")
                # 对于蓝牙命令，如果响应明确表示蓝牙状态，也认为是有效响应
                if "bluetooth" in command_lower and any(keyword in response_lower for keyword in ["蓝牙", "bluetooth"]):
                    log.info("✅ 蓝牙命令得到了蓝牙状态响应，认为是有效响应")
                    return True
                return False

        except Exception as e:
            log.error(f"验证命令响应失败: {e}")
            return False

    def check_alarm_status(self) -> bool:
        """
        检查闹钟状态 - 通过ADB命令获取闹钟列表

        Returns:
            bool: 是否有设置的闹钟
        """
        try:
            log.info("检查闹钟状态")

            # 通过ADB命令检查闹钟设置
            import subprocess

            # 方法1: 通过dumpsys alarm检查
            result = subprocess.run(
                ["adb", "shell", "dumpsys", "alarm"],
                capture_output=True,
                text=True,
                timeout=10
            )

            if result.returncode == 0:
                alarm_output = result.stdout
                # 检查是否包含闹钟相关信息
                alarm_indicators = [
                    "com.transsion.deskclock",
                    "AlarmManager",
                    "alarm_clock",
                    "RTC_WAKEUP"
                ]

                for indicator in alarm_indicators:
                    if indicator in alarm_output:
                        log.info(f"检测到闹钟相关信息: {indicator}")
                        return True

                log.info("未检测到活跃的闹钟")
                return False
            else:
                log.error(f"获取闹钟状态失败: {result.stderr}")
                return False

        except Exception as e:
            log.error(f"检查闹钟状态失败: {e}")
            return False

    def check_alarm_status_smart(self) -> bool:
        """
        智能检查闹钟状态 - 包含进程检测

        Returns:
            bool: 是否有设置的闹钟
        """
        try:
            log.info("智能检查闹钟状态...")

            # 确保在Ella进程
            if not self.ensure_ella_process():
                log.warning("检查闹钟状态时不在Ella进程，尝试返回")
                if not self.return_to_ella_app():
                    log.error("无法返回Ella应用")
                    return False

            # 调用原有的闹钟状态检查方法
            return self.check_alarm_status()

        except Exception as e:
            log.error(f"智能检查闹钟状态失败: {e}")
            return False

    def verify_alarm_set(self, target_time: str = "10:00") -> bool:
        """
        验证闹钟是否设置成功

        Args:
            target_time: 目标时间，格式如 "10:00"

        Returns:
            bool: 闹钟是否设置成功
        """
        try:
            log.info(f"验证闹钟设置: {target_time}")

            # 方法1: 检查时钟应用是否打开
            clock_package = "com.transsion.deskclock"

            # 检查时钟应用进程
            import subprocess
            result = subprocess.run(
                ["adb", "shell", "dumpsys", "activity", "activities"],
                capture_output=True,
                text=True,
                timeout=10
            )

            if result.returncode == 0 and clock_package in result.stdout:
                log.info("✅ 时钟应用已打开，可能正在设置闹钟")

                # 方法2: 通过UI检查是否有闹钟设置界面
                try:
                    # 检查是否有闹钟相关的UI元素
                    alarm_elements = [
                        self.driver(text="闹钟"),
                        self.driver(text="Alarm"),
                        self.driver(text="设置闹钟"),
                        self.driver(text="Set alarm"),
                        self.driver(resourceId="com.transsion.deskclock:id/alarm"),
                        self.driver(className="android.widget.TimePicker")
                    ]

                    for element in alarm_elements:
                        if element.exists():
                            log.info(f"✅ 检测到闹钟设置界面元素")
                            return True

                except Exception as e:
                    log.debug(f"UI检查异常: {e}")

                return True  # 时钟应用打开就认为设置成功

            # 方法3: 检查系统闹钟状态
            return self.check_alarm_status()

        except Exception as e:
            log.error(f"验证闹钟设置失败: {e}")
            return False

    def get_alarm_list(self) -> list:
        """
        获取当前设置的闹钟列表

        Returns:
            list: 闹钟列表，包含闹钟的详细信息
        """
        try:
            log.info("获取闹钟列表")

            import subprocess

            # 方法1: 通过dumpsys获取闹钟信息
            result = subprocess.run(
                ["adb", "shell", "dumpsys", "alarm"],
                capture_output=True,
                text=True,
                timeout=15
            )

            alarms = []

            if result.returncode == 0:
                alarm_output = result.stdout

                # 解析闹钟信息（简化版本）
                lines = alarm_output.split('\n')
                for line in lines:
                    if "com.transsion.deskclock" in line and "RTC_WAKEUP" in line:
                        alarms.append(line.strip())

                log.info(f"通过dumpsys找到 {len(alarms)} 个闹钟")

            # 方法2: 尝试通过时钟应用数据库获取更详细信息
            try:
                db_result = subprocess.run(
                    ["adb", "shell", "content", "query", "--uri", "content://com.android.deskclock/alarms"],
                    capture_output=True,
                    text=True,
                    timeout=10
                )

                if db_result.returncode == 0 and db_result.stdout.strip():
                    db_alarms = self._parse_alarm_database_output(db_result.stdout)
                    if db_alarms:
                        log.info(f"通过数据库找到 {len(db_alarms)} 个闹钟")
                        return db_alarms

            except Exception as e:
                log.debug(f"数据库查询失败: {e}")

            return alarms

        except Exception as e:
            log.error(f"获取闹钟列表失败: {e}")
            return []

    def _parse_alarm_database_output(self, output: str) -> list:
        """
        解析闹钟数据库输出

        Args:
            output: 数据库查询输出

        Returns:
            list: 解析后的闹钟列表
        """
        try:
            alarms = []
            lines = output.strip().split('\n')

            for line in lines:
                if 'hour=' in line and 'minutes=' in line:
                    # 解析闹钟时间信息
                    alarm_info = {}

                    # 提取小时
                    if 'hour=' in line:
                        hour_start = line.find('hour=') + 5
                        hour_end = line.find(',', hour_start)
                        if hour_end == -1:
                            hour_end = line.find(' ', hour_start)
                        if hour_end != -1:
                            alarm_info['hour'] = line[hour_start:hour_end].strip()

                    # 提取分钟
                    if 'minutes=' in line:
                        min_start = line.find('minutes=') + 8
                        min_end = line.find(',', min_start)
                        if min_end == -1:
                            min_end = line.find(' ', min_start)
                        if min_end != -1:
                            alarm_info['minutes'] = line[min_start:min_end].strip()

                    # 提取启用状态
                    if 'enabled=' in line:
                        enabled_start = line.find('enabled=') + 8
                        enabled_end = line.find(',', enabled_start)
                        if enabled_end == -1:
                            enabled_end = line.find(' ', enabled_start)
                        if enabled_end != -1:
                            alarm_info['enabled'] = line[enabled_start:enabled_end].strip()

                    if alarm_info:
                        alarms.append(alarm_info)

            return alarms

        except Exception as e:
            log.error(f"解析闹钟数据库输出失败: {e}")
            return []

    def clear_all_alarms(self) -> bool:
        """
        清空所有闹钟

        Returns:
            bool: 是否成功清空
        """
        try:
            log.info("开始清空所有闹钟...")

            import subprocess

            # 记录清空前的状态
            initial_alarms = self.get_alarm_list()
            log.info(f"清空前发现 {len(initial_alarms)} 个闹钟")

            # 方法1: 通过时钟应用清空闹钟
            clock_package = "com.transsion.deskclock"

            # 先停止时钟应用
            log.info("停止时钟应用")
            subprocess.run(
                ["adb", "shell", "am", "force-stop", clock_package],
                capture_output=True,
                timeout=5
            )
            time.sleep(1)

            # 方法2: 通过应用数据清理（最彻底的方法）
            log.info("清理时钟应用数据")
            clear_result = subprocess.run(
                ["adb", "shell", "pm", "clear", clock_package],
                capture_output=True,
                text=True,
                timeout=10
            )

            if clear_result.returncode == 0:
                log.info("✅ 时钟应用数据已清理")
                time.sleep(3)  # 等待清理完成

                # 验证清理结果
                remaining_alarms = self.get_alarm_list()
                log.info(f"清理后剩余 {len(remaining_alarms)} 个闹钟")

                if len(remaining_alarms) < len(initial_alarms):
                    log.info(f"✅ 闹钟数量减少: {len(initial_alarms)} -> {len(remaining_alarms)}")
                    return True
                elif not remaining_alarms:
                    log.info("✅ 所有闹钟已清空")
                    return True
                else:
                    log.warning(f"⚠️ 清理后仍有 {len(remaining_alarms)} 个闹钟")

            # 方法3: 尝试通过数据库删除（备用方法）
            try:
                log.info("尝试通过数据库删除闹钟")
                delete_result = subprocess.run(
                    ["adb", "shell", "content", "delete", "--uri", "content://com.android.deskclock/alarms"],
                    capture_output=True,
                    text=True,
                    timeout=10
                )

                if delete_result.returncode == 0:
                    log.info("✅ 数据库删除命令执行成功")
                    time.sleep(2)

                    final_alarms = self.get_alarm_list()
                    if len(final_alarms) < len(initial_alarms):
                        log.info(f"✅ 通过数据库删除减少了闹钟: {len(initial_alarms)} -> {len(final_alarms)}")
                        return True

            except Exception as e:
                log.warning(f"数据库删除失败: {e}")

            # 如果所有方法都失败，记录详细信息但不阻止测试继续
            final_alarms = self.get_alarm_list()
            log.warning(f"⚠️ 闹钟清空未完全成功，但测试将继续")
            log.info(f"最终闹钟状态: {len(final_alarms)} 个")

            # 如果闹钟数量有所减少，也认为是部分成功
            return len(final_alarms) < len(initial_alarms)

        except Exception as e:
            log.error(f"清空闹钟失败: {e}")
            return False

    def verify_alarm_in_list(self, target_time: str = "10:00") -> bool:
        """
        验证指定时间的闹钟是否在闹钟列表中

        Args:
            target_time: 目标时间，格式如 "10:00"

        Returns:
            bool: 闹钟是否在列表中
        """
        try:
            log.info(f"验证闹钟是否在列表中: {target_time}")

            # 解析目标时间
            if ":" in target_time:
                target_hour, target_minute = target_time.split(":")
                target_hour = int(target_hour)
                target_minute = int(target_minute)
            else:
                log.error(f"时间格式错误: {target_time}")
                return False

            # 获取闹钟列表
            alarm_list = self.get_alarm_list()

            if not alarm_list:
                log.warning("闹钟列表为空")
                return False

            log.info(f"检查 {len(alarm_list)} 个闹钟记录")

            # 检查是否有匹配的闹钟
            for i, alarm in enumerate(alarm_list):
                log.debug(f"检查闹钟 {i+1}: {alarm}")

                if isinstance(alarm, dict):
                    # 数据库格式的闹钟信息
                    try:
                        alarm_hour = int(alarm.get('hour', -1))
                        alarm_minute = int(alarm.get('minutes', -1))

                        if alarm_hour == target_hour and alarm_minute == target_minute:
                            log.info(f"✅ 找到匹配的闹钟: {alarm_hour:02d}:{alarm_minute:02d}")
                            return True

                    except (ValueError, TypeError):
                        continue

                elif isinstance(alarm, str):
                    # dumpsys格式的闹钟信息，尝试多种匹配方式
                    alarm_lower = alarm.lower()

                    # 方式1: 直接匹配时间格式
                    time_patterns = [
                        f"{target_hour:02d}:{target_minute:02d}",
                        f"{target_hour}:{target_minute:02d}",
                        f"{target_hour:02d}:{target_minute}",
                        f"{target_hour}:{target_minute}"
                    ]

                    for pattern in time_patterns:
                        if pattern in alarm:
                            log.info(f"✅ 在闹钟记录中找到匹配时间: {pattern}")
                            return True

                    # 方式2: 检查是否包含小时和分钟信息
                    import re

                    # 查找时间相关的模式
                    time_matches = re.findall(r'(\d{1,2}):(\d{2})', alarm)
                    for hour_str, minute_str in time_matches:
                        try:
                            hour = int(hour_str)
                            minute = int(minute_str)
                            if hour == target_hour and minute == target_minute:
                                log.info(f"✅ 通过正则表达式找到匹配时间: {hour:02d}:{minute:02d}")
                                return True
                        except ValueError:
                            continue

                    # 方式3: 检查明天10点的特殊情况
                    if target_hour == 10 and target_minute == 0:
                        tomorrow_patterns = [
                            "10:00", "10am", "10 am", "tomorrow", "明天"
                        ]
                        for pattern in tomorrow_patterns:
                            if pattern in alarm_lower:
                                log.info(f"✅ 找到明天10点相关的闹钟模式: {pattern}")
                                return True

            log.warning(f"⚠️ 未找到匹配的闹钟: {target_time}")
            log.info("闹钟列表详情:")
            for i, alarm in enumerate(alarm_list):
                log.info(f"  闹钟 {i+1}: {alarm}")

            return False

        except Exception as e:
            log.error(f"验证闹钟列表失败: {e}")
            return False

    def verify_weather_response(self, response_text: str, location: str = "shanghai") -> bool:
        """
        验证天气响应是否包含有效的天气信息

        Args:
            response_text: AI响应文本
            location: 查询的城市名称

        Returns:
            bool: 响应是否包含有效天气信息
        """
        try:
            if not response_text:
                log.warning("响应文本为空")
                return False

            response_lower = response_text.lower()
            log.info(f"验证天气响应: {location}")

            # 检查是否包含位置信息
            location_keywords = [
                location.lower(), "shanghai", "上海", "city", "城市"
            ]

            location_found = any(keyword in response_lower for keyword in location_keywords)
            if location_found:
                log.info(f"✅ 响应包含位置信息")

            # 检查是否包含天气相关信息
            weather_indicators = [
                # 基本天气词汇
                "weather", "天气", "temperature", "温度", "degree", "度",
                "celsius", "摄氏度", "fahrenheit", "华氏度", "°c", "°f",

                # 天气状况
                "sunny", "晴", "cloudy", "云", "rainy", "雨", "snow", "雪",
                "clear", "晴朗", "overcast", "阴", "fog", "雾", "mist", "薄雾",

                # 天气数据
                "humidity", "湿度", "wind", "风", "pressure", "气压",
                "visibility", "能见度", "precipitation", "降水",

                # 时间相关
                "today", "今天", "tomorrow", "明天", "forecast", "预报",
                "current", "当前", "now", "现在"
            ]

            found_weather_indicators = []
            for indicator in weather_indicators:
                if indicator in response_lower:
                    found_weather_indicators.append(indicator)

            if found_weather_indicators:
                log.info(f"✅ 响应包含天气指标: {found_weather_indicators[:5]}...")  # 只显示前5个

                # 如果同时包含位置和天气信息，认为是有效的天气响应
                if location_found and len(found_weather_indicators) >= 2:
                    log.info("✅ 响应包含完整的天气信息（位置+天气数据）")
                    return True
                elif len(found_weather_indicators) >= 3:
                    log.info("✅ 响应包含丰富的天气信息")
                    return True
                else:
                    log.info("✅ 响应包含基本天气信息")
                    return True
            else:
                log.warning("⚠️ 响应未包含明显的天气信息")
                return False

        except Exception as e:
            log.error(f"验证天气响应失败: {e}")
            return False

    def check_weather_app_opened(self) -> bool:
        """
        检查是否有天气应用被打开

        Returns:
            bool: 是否有天气应用打开
        """
        try:
            log.info("检查天气应用状态")

            # 常见的天气应用包名
            weather_packages = [
                "com.miui.weather",
                "com.android.weather",
                "com.google.android.apps.weather",
                "com.transsion.weather",
                "com.weather.forecast",
                "com.accuweather",
                "com.weather.channel",
                "weather"
            ]

            # 使用优化的AdbProcessMonitor进行检测
            for package in weather_packages:
                if self.process_monitor.is_package_running(package):
                    log.info(f"✅ 检测到天气应用: {package}")
                    return True

            log.info("未检测到专门的天气应用")
            return False

        except Exception as e:
            log.error(f"检查天气应用失败: {e}")
            return False

    def verify_weather_query_success(self, response_text: str, location: str = "shanghai") -> dict:
        """
        综合验证天气查询是否成功

        Args:
            response_text: AI响应文本
            location: 查询的城市名称

        Returns:
            dict: 验证结果详情
        """
        try:
            log.info(f"综合验证天气查询: {location}")

            result = {
                "response_valid": False,
                "weather_info_found": False,
                "location_mentioned": False,
                "weather_app_opened": False,
                "overall_success": False,
                "details": []
            }

            # 1. 验证响应有效性
            if response_text and len(response_text.strip()) > 0:
                result["response_valid"] = True
                result["details"].append("✅ 收到有效响应")
            else:
                result["details"].append("❌ 响应为空或无效")

            # 2. 验证天气信息
            if self.verify_weather_response(response_text, location):
                result["weather_info_found"] = True
                result["details"].append("✅ 响应包含天气信息")
            else:
                result["details"].append("⚠️ 响应未包含明显天气信息")

            # 3. 检查位置信息
            if response_text:
                response_lower = response_text.lower()
                location_keywords = [location.lower(), "shanghai", "上海"]
                if any(keyword in response_lower for keyword in location_keywords):
                    result["location_mentioned"] = True
                    result["details"].append(f"✅ 响应提及位置: {location}")
                else:
                    result["details"].append(f"⚠️ 响应未明确提及位置: {location}")

            # 4. 检查天气应用
            if self.check_app_opened(AppType.WEATHER):
                result["weather_app_opened"] = True
                result["details"].append("✅ 检测到天气应用打开")
            else:
                result["details"].append("ℹ️ 未检测到天气应用打开")

            # 5. 综合判断
            success_indicators = [
                result["response_valid"],
                result["weather_info_found"]
            ]

            # 如果响应有效且包含天气信息，认为查询成功
            if all(success_indicators):
                result["overall_success"] = True
                result["details"].append("🎉 天气查询成功")
            else:
                result["details"].append("⚠️ 天气查询可能未完全成功")

            log.info(f"天气查询验证结果: {result['overall_success']}")
            for detail in result["details"]:
                log.info(f"  {detail}")

            return result

        except Exception as e:
            log.error(f"综合验证天气查询失败: {e}")
            return {
                "response_valid": False,
                "weather_info_found": False,
                "location_mentioned": False,
                "weather_app_opened": False,
                "overall_success": False,
                "details": [f"❌ 验证过程异常: {e}"]
            }

    def check_camera_app_opened(self) -> bool:
        """
        检查是否有相机应用被打开

        Returns:
            bool: 是否有相机应用打开
        """
        try:
            log.info("检查相机应用状态")

            # 常见的相机应用包名
            camera_packages = [
                "com.android.camera",
                "com.google.android.GoogleCamera",
                "com.transsion.camera",
                "com.sec.android.app.camera",
                "com.huawei.camera",
                "com.xiaomi.camera",
                "com.oppo.camera",
                "com.vivo.camera",
                "camera",
                "Camera"
            ]

            # 使用优化的AdbProcessMonitor进行检测
            for package in camera_packages:
                if self.process_monitor.is_package_running(package):
                    log.info(f"✅ 检测到相机应用: {package}")
                    return True

            log.info("未检测到专门的相机应用")
            return False

        except Exception as e:
            log.error(f"检查相机应用失败: {e}")
            return False

    def check_contacts_app_opened(self) -> bool:
        """
        检查是否有联系人应用被打开

        Returns:
            bool: 是否有联系人应用打开
        """
        try:
            log.info("检查联系人应用状态")

            # 常见的联系人应用包名
            contacts_packages = [
                "com.sh.smart.caller"
            ]

            # 使用优化的AdbProcessMonitor进行检测
            for package in contacts_packages:
                if self.process_monitor.is_package_running(package):
                    log.info(f"✅ 检测到联系人应用: {package}")
                    return True

            log.info("未检测到专门的联系人应用")
            return False

        except Exception as e:
            log.error(f"检查联系人应用失败: {e}")
            return False

    def check_contacts_app_opened_smart(self) -> bool:
        """
        智能检查联系人应用状态 - 包含进程检测

        Returns:
            bool: 联系人应用是否打开
        """
        try:
            log.info("智能检查联系人应用状态...")

            # 确保在Ella进程
            if not self.ensure_ella_process():
                log.warning("检查联系人应用状态时不在Ella进程，尝试返回")
                if not self.return_to_ella_app():
                    log.error("无法返回Ella应用")
                    return False

            # 调用原有的联系人应用状态检查方法
            return self.check_app_opened(AppType.CONTACTS)

        except Exception as e:
            log.error(f"智能检查联系人应用状态失败: {e}")
            return False

    def check_camera_permission(self) -> bool:
        """
        检查相机权限状态

        Returns:
            bool: 是否有相机权限
        """
        try:
            log.info("检查相机权限状态")

            import subprocess

            # 检查Ella应用的相机权限
            result = subprocess.run(
                ["adb", "shell", "dumpsys", "package", "com.transsion.aivoiceassistant"],
                capture_output=True,
                text=True,
                timeout=10
            )

            if result.returncode == 0:
                package_output = result.stdout

                # 查找相机权限相关信息
                camera_permission_indicators = [
                    "android.permission.CAMERA",
                    "CAMERA: granted=true",
                    "camera permission"
                ]

                for indicator in camera_permission_indicators:
                    if indicator in package_output:
                        log.info(f"✅ 检测到相机权限信息: {indicator}")
                        return True

                log.info("未检测到相机权限信息")
                return False
            else:
                log.error(f"获取权限信息失败: {result.stderr}")
                return False

        except Exception as e:
            log.error(f"检查相机权限失败: {e}")
            return False

    def verify_photo_response(self, response_text: str) -> bool:
        """
        验证拍照响应是否包含有效的拍照信息

        Args:
            response_text: AI响应文本

        Returns:
            bool: 响应是否包含有效拍照信息
        """
        try:
            if not response_text:
                log.warning("响应文本为空")
                return False

            response_lower = response_text.lower()
            log.info("验证拍照响应")

            # 检查是否包含拍照相关信息
            photo_indicators = [
                # 基本拍照词汇
                "photo", "拍照", "picture", "照片", "camera", "相机", "摄像头",
                "take", "拍", "capture", "捕获", "shot", "拍摄", "snap", "快照",
                "image", "图像", "pic", "图片", "photograph", "摄影",

                # 拍照动作
                "taking", "正在拍", "capturing", "正在捕获", "shooting", "正在拍摄",
                "snapping", "正在快照", "photographing", "正在摄影",

                # 拍照结果
                "taken", "已拍", "captured", "已捕获", "saved", "已保存",
                "completed", "完成", "done", "完毕", "success", "成功",

                # 相机相关
                "lens", "镜头", "focus", "对焦", "flash", "闪光灯",
                "shutter", "快门", "exposure", "曝光"
            ]

            found_photo_indicators = []
            for indicator in photo_indicators:
                if indicator in response_lower:
                    found_photo_indicators.append(indicator)

            if found_photo_indicators:
                log.info(f"✅ 响应包含拍照指标: {found_photo_indicators[:5]}...")  # 只显示前5个

                if len(found_photo_indicators) >= 2:
                    log.info("✅ 响应包含丰富的拍照信息")
                    return True
                else:
                    log.info("✅ 响应包含基本拍照信息")
                    return True
            else:
                log.warning("⚠️ 响应未包含明显的拍照信息")
                return False

        except Exception as e:
            log.error(f"验证拍照响应失败: {e}")
            return False

    def verify_photo_taken_success(self, response_text: str) -> dict:
        """
        综合验证拍照是否成功

        Args:
            response_text: AI响应文本

        Returns:
            dict: 验证结果详情
        """
        try:
            log.info("综合验证拍照操作")

            result = {
                "response_valid": False,
                "photo_info_found": False,
                "camera_app_opened": False,
                "camera_permission": False,
                "overall_success": False,
                "details": []
            }

            # 1. 验证响应有效性
            if response_text and len(response_text.strip()) > 0:
                result["response_valid"] = True
                result["details"].append("✅ 收到有效响应")
            else:
                result["details"].append("❌ 响应为空或无效")

            # 2. 验证拍照信息
            if self.verify_photo_response(response_text):
                result["photo_info_found"] = True
                result["details"].append("✅ 响应包含拍照信息")
            else:
                result["details"].append("⚠️ 响应未包含明显拍照信息")

            # 3. 检查相机应用
            if self.check_app_opened(AppType.CAMERA):
                result["camera_app_opened"] = True
                result["details"].append("✅ 检测到相机应用打开")
            else:
                result["details"].append("ℹ️ 未检测到相机应用打开")

            # 4. 检查相机权限
            if self.check_camera_permission():
                result["camera_permission"] = True
                result["details"].append("✅ 检测到相机权限")
            else:
                result["details"].append("⚠️ 未检测到相机权限")

            # 5. 综合判断
            success_indicators = [
                result["response_valid"],
                result["photo_info_found"]
            ]

            # 如果响应有效且包含拍照信息，认为拍照成功
            if all(success_indicators):
                result["overall_success"] = True
                result["details"].append("🎉 拍照操作成功")
            elif result["camera_app_opened"]:
                result["overall_success"] = True
                result["details"].append("🎉 相机应用已打开，拍照可能成功")
            else:
                result["details"].append("⚠️ 拍照操作可能未完全成功")

            log.info(f"拍照验证结果: {result['overall_success']}")
            for detail in result["details"]:
                log.info(f"  {detail}")

            return result

        except Exception as e:
            log.error(f"综合验证拍照失败: {e}")
            return {
                "response_valid": False,
                "photo_info_found": False,
                "camera_app_opened": False,
                "camera_permission": False,
                "overall_success": False,
                "details": [f"❌ 验证过程异常: {e}"]
            }


if __name__ == '__main__':
    ella_page = EllaMainPage()
    ella_page.start_app_with_activity()
    # ella_page.wait_for_page_load()
    # ella_page.check_bluetooth_status()
